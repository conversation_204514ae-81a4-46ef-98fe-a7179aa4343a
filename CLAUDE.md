# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Terraform-managed Cloud Functions project that automatically processes PDF books into individual chapter JSON files and generates audio files via text-to-speech processing. The system uses two Cloud Functions: PDF processing and TTS processing, connected via Pub/Sub messaging.

## Development Commands

### Environment Setup
```bash
# Navigate to specific function directory
cd functions/pdf-to-text     # For PDF processing function
cd functions/chapter-tts     # For TTS processing function

# Each function has its own requirements.txt and dependencies
# No shared virtual environment needed
```

### Code Quality & Testing
```bash
# Format code (in function directory)
black .

# Type checking
mypy main.py

# Run tests (if test files exist)
pytest
```

### Terraform Operations
```bash
# Deploy complete infrastructure
cd terraform
export TF_VAR_project_id=fonos-audio
./deploy-dev.sh

# Update functions only
./update-functions.sh dev

# View function logs
gcloud functions logs read --region=asia-southeast1 --project=fonos-audio

# Check function status
gcloud functions describe chapter-tts-dev --region=asia-southeast1 --project=fonos-audio
```

## Architecture

### Core Components

1. **functions/pdf-to-text/** - PDF Processing Function:
   - **main.py** - `process_gcs_file()` entry point with GCS trigger
   - **pdf_to_chapter.py** - PDF-to-text conversion using Google Generative AI
   - Triggered by PDF uploads to `books/[id]/book.pdf` pattern
   - Publishes chapter messages to Pub/Sub topic

2. **functions/chapter-tts/** - TTS Processing Function:
   - **main.py** - `process_chapter_tts()` entry point with Pub/Sub trigger
   - **google-voice/** - TTS processor from git submodule (google-voice)
   - **build.sh** - Syncs git submodule before deployment
   - **Dockerfile** - Containerized with ffmpeg for audio processing
   - Processes individual chapters and generates audio files

3. **functions/shared/** - Common utilities:
   - **firestore_schema.py** - Data models for Firestore integration

### Processing Flow

1. **PDF Upload** → `gs://bucket/books/[book_id]/book.pdf`
2. **PDF Function Triggers** → GCS event trigger activates PDF processing
3. **AI Analysis** → Extracts table of contents and chapter structure  
4. **Chapter Messages** → Published to Pub/Sub topic for each chapter
5. **TTS Function Triggers** → Pub/Sub messages activate TTS processing
6. **Audio Generation** → Each chapter converted to audio file
7. **Progress Tracking** → Firestore documents track processing status

### Output Structure
```
books/[book_id]/
├── book.pdf                     # Original PDF
├── book_manifest.json           # Processing status & outline
├── text-extracted-chapters/     # Chapter JSON files
│   ├── chapter_01.json
│   ├── chapter_02.json
│   └── ...
└── audio/                       # Generated audio files
    ├── chapter_01.mp3
    ├── chapter_02.mp3
    └── ...
```

## Configuration Requirements

### Required Environment Variables
- `GEMINI_API_KEY` - Google Generative AI API key (stored in Secret Manager)
- `ENVIRONMENT` - Environment name (dev/staging/prod)
- `DATA_BUCKET_NAME` - GCS bucket name for file storage
- `CHAPTER_TOPIC_NAME` - Pub/Sub topic for chapter processing

### Database Configuration
- **Firestore Database ID**: `asia-southeast-1`
- All Firestore clients configured to use this specific database region
- Collection structure: `books/{bookId}/chapters/{chapterUUID}`

### Terraform Project Setup
1. Configure project ID in `terraform/envs/*/terraform.tfvars`
2. Set up GCP authentication: `gcloud auth login`
3. Enable required APIs via deployment script
4. Deploy infrastructure: `cd terraform && ./deploy-dev.sh`

## Key Development Patterns

### Function Organization
- **Separate directories** for each function with own dependencies
- **Docker containers** for functions requiring system libraries (ffmpeg)
- **Git submodule integration** via build scripts for external dependencies
- **Shared utilities** in common folder for code reuse

### Error Handling
- Comprehensive try/catch blocks with detailed logging
- Failed chapters don't prevent others from completing  
- Idempotent processing with correlation IDs
- Dead letter queues for failed messages

### Event-Driven Architecture
- **GCS triggers** for PDF uploads
- **Pub/Sub messaging** between functions
- **Firestore integration** for state management
- **Async processing** with parallel chapter handling

### Container Integration
- **chapter-tts function** uses Docker with ffmpeg system installation
- **build.sh scripts** sync google-voice git submodule before deployment
- **Terraform manages** containerized function deployment
- **git submodule** integration for google-voice TTS processor

## Dependencies Management

### PDF-to-Text Function
```bash
cd functions/pdf-to-text
pip install -r requirements.txt
```

### Chapter-TTS Function  
```bash
cd functions/chapter-tts
pip install -r requirements.txt
# Docker handles ffmpeg and system dependencies
```

### TTS Processor Integration
```bash
cd functions/chapter-tts
./build.sh  # Syncs google-voice git submodule

# Manual submodule operations (if needed)
git submodule update --init --recursive google-voice
git submodule status  # Check submodule status

# Docker build (handles submodule automatically)
docker build -t chapter-tts .
```

Project uses:
- **Terraform** for infrastructure as code
- **Cloud Functions Gen 2** with containerization support
- **Organized function structure** with separate dependencies
- **Git submodule** for google-voice TTS processor integration
- **GCS, Pub/Sub, Firestore** for event-driven processing