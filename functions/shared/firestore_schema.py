"""
Firestore Data Model Schema
Defines the data structures matching the Terraform migration plan.
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime
import firebase_admin
from firebase_admin import firestore


@dataclass
class BookDocument:
    """
    Main book document schema
    Collection: books-tts (doc: {bookId})
    """
    title: str
    author: Optional[str] = None
    language: str = 'vi-VN'
    source_pdf_path: str = ''  # Relative path: /books/book123/book.pdf
    status: str = 'new'  # new|outline_extracting|outline_extracted|text_extracting|text_extracted|tts_processing|completed|error
    chapter_count: int = 0
    completed_chapters: int = 0
    extracted_chapters: int = 0  # Counter for text extraction completion
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_error: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_firestore_dict(self) -> Dict[str, Any]:
        """Convert to Firestore-compatible dictionary"""
        data = {
            'title': self.title,
            'language': self.language,
            'sourcePdfPath': self.source_pdf_path,
            'status': self.status,
            'chapterCount': self.chapter_count,
            'completedChapters': self.completed_chapters,
            'extractedChapters': self.extracted_chapters,
        }
        
        if self.author:
            data['author'] = self.author
        
        if self.last_error:
            data['lastError'] = self.last_error
        
        if self.metadata:
            data['metadata'] = self.metadata
        
        # Firestore timestamps will be set by server
        if not self.created_at:
            data['createdAt'] = firestore.SERVER_TIMESTAMP
        
        data['updatedAt'] = firestore.SERVER_TIMESTAMP
        
        return data


@dataclass
class ChapterDocument:
    """
    Chapter document schema
    Collection: books-tts/{bookId}/chapters (doc: {chapterUUID})
    """
    book_id: str
    title: str
    order_index: int  # Integer ordering: 1, 2, 3...
    content: str  # Full chapter text content stored directly
    content_checksum: Optional[str] = None  # SHA256 of content
    audio_path: Optional[str] = None  # Relative path: /books/book123/audio/uuid.mp3
    audio_format: Optional[str] = None
    duration_ms: Optional[int] = None
    audio_size_bytes: Optional[int] = None
    status: str = 'pending_extraction'  # pending_extraction|extracting|extracted|tts_queued|tts_processing|completed|error
    attempts: int = 0
    last_error: Optional[Dict[str, Any]] = None
    correlation_id: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    start_page: Optional[int] = None
    end_page: Optional[int] = None
    word_count: Optional[int] = None
    tts_settings: Optional[Dict[str, Any]] = None  # TTS configuration

    def to_firestore_dict(self) -> Dict[str, Any]:
        """Convert to Firestore-compatible dictionary"""
        data = {
            'bookId': self.book_id,
            'title': self.title,
            'orderIndex': self.order_index,
            'content': self.content,
            'status': self.status,
            'attempts': self.attempts,
        }
        
        # Add optional string fields
        optional_str_fields = {
            'contentChecksum': self.content_checksum,
            'audioPath': self.audio_path,
            'audioFormat': self.audio_format,
            'correlationId': self.correlation_id,
        }
        
        for field_name, value in optional_str_fields.items():
            if value:
                data[field_name] = value
        
        # Add optional numeric fields
        optional_num_fields = {
            'durationMs': self.duration_ms,
            'audioSizeBytes': self.audio_size_bytes,
            'startPage': self.start_page,
            'endPage': self.end_page,
            'wordCount': self.word_count,
        }
        
        for field_name, value in optional_num_fields.items():
            if value is not None:
                data[field_name] = value
        
        # Add error information
        if self.last_error:
            data['lastError'] = self.last_error
        
        # Add TTS settings
        if self.tts_settings:
            data['ttsSettings'] = self.tts_settings
        
        # Handle timestamps
        if not self.created_at:
            data['createdAt'] = firestore.SERVER_TIMESTAMP
        
        data['updatedAt'] = firestore.SERVER_TIMESTAMP
        
        if self.started_at:
            data['startedAt'] = self.started_at
        
        if self.completed_at:
            data['completedAt'] = self.completed_at
        
        return data


class FirestoreSchema:
    """
    Utility class for managing Firestore schema operations
    Note: Use firestore.Client(database="asia-southeast-1") when initializing
    """
    
    def __init__(self, firestore_client: firestore.Client):
        self.db = firestore_client
    
    def create_book(self, book_id: str, book_data: BookDocument) -> None:
        """Create a new book document"""
        book_ref = self.db.collection('books-tts').document(book_id)
        book_ref.set(book_data.to_firestore_dict())
    
    def update_book(self, book_id: str, updates: Dict[str, Any]) -> None:
        """Update book document with specific fields"""
        book_ref = self.db.collection('books-tts').document(book_id)
        
        # Always update the timestamp
        updates['updatedAt'] = firestore.SERVER_TIMESTAMP
        
        book_ref.set(updates, merge=True)
    
    def get_book(self, book_id: str) -> Optional[Dict[str, Any]]:
        """Get book document"""
        book_ref = self.db.collection('books').document(book_id)
        doc = book_ref.get()
        return doc.to_dict() if doc.exists else None
    
    def create_chapter(self, book_id: str, chapter_id: str, chapter_data: ChapterDocument) -> None:
        """Create a new chapter document"""
        chapter_ref = (
            self.db.collection('books')
            .document(book_id)
            .collection('chapters')
            .document(chapter_id)
        )
        chapter_ref.set(chapter_data.to_firestore_dict())
    
    def update_chapter(self, book_id: str, chapter_id: str, updates: Dict[str, Any]) -> None:
        """Update chapter document with specific fields"""
        chapter_ref = (
            self.db.collection('books')
            .document(book_id)
            .collection('chapters')
            .document(chapter_id)
        )
        
        # Always update the timestamp
        updates['updatedAt'] = firestore.SERVER_TIMESTAMP
        
        chapter_ref.set(updates, merge=True)
    
    def get_chapter(self, book_id: str, chapter_id: str) -> Optional[Dict[str, Any]]:
        """Get chapter document"""
        chapter_ref = (
            self.db.collection('books')
            .document(book_id)
            .collection('chapters')
            .document(chapter_id)
        )
        doc = chapter_ref.get()
        return doc.to_dict() if doc.exists else None
    
    def get_chapters_by_status(self, book_id: str, status: str) -> List[Dict[str, Any]]:
        """Get chapters by status for a specific book"""
        chapters_ref = (
            self.db.collection('books')
            .document(book_id)
            .collection('chapters')
            .where('status', '==', status)
            .order_by('orderIndex')
        )
        
        return [doc.to_dict() for doc in chapters_ref.stream()]
    
    def get_chapters_by_book_ordered(self, book_id: str) -> List[Dict[str, Any]]:
        """Get all chapters for a book ordered by chapter order"""
        chapters_ref = (
            self.db.collection('books')
            .document(book_id)
            .collection('chapters')
            .order_by('orderIndex')
        )
        
        return [doc.to_dict() for doc in chapters_ref.stream()]
    
    def get_recent_chapter_errors(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent chapter errors across all books"""
        # Using collection group query for chapters
        chapters_ref = (
            self.db.collection_group('chapters')
            .where('status', '==', 'error')
            .order_by('updatedAt', direction=firestore.Query.DESCENDING)
            .limit(limit)
        )
        
        results = []
        for doc in chapters_ref.stream():
            chapter_data = doc.to_dict()
            chapter_data['id'] = doc.id
            chapter_data['book_id'] = doc.reference.parent.parent.id
            results.append(chapter_data)
        
        return results
    
    def get_processing_backlog(self, status: str = 'queued') -> List[Dict[str, Any]]:
        """Get chapters waiting for processing"""
        chapters_ref = (
            self.db.collection_group('chapters')
            .where('status', '==', status)
            .order_by('createdAt')
        )
        
        results = []
        for doc in chapters_ref.stream():
            chapter_data = doc.to_dict()
            chapter_data['id'] = doc.id
            chapter_data['book_id'] = doc.reference.parent.parent.id
            results.append(chapter_data)
        
        return results
    
    def increment_extracted_chapters(self, book_id: str) -> Dict[str, Any]:
        """
        Atomically increment extracted chapters counter and check completion.
        Returns the updated book document.
        """
        book_ref = self.db.collection('books-tts').document(book_id)
        
        @firestore.transactional
        def update_in_transaction(transaction):
            # Get current book state
            book_doc = book_ref.get(transaction=transaction)
            if not book_doc.exists:
                raise ValueError(f"Book {book_id} not found")
            
            book_data = book_doc.to_dict()
            
            # Increment extracted chapters counter
            new_extracted_count = book_data.get('extractedChapters', 0) + 1
            total_chapters = book_data.get('chapterCount', 0)
            
            updates = {
                'extractedChapters': new_extracted_count,
                'updatedAt': firestore.SERVER_TIMESTAMP
            }
            
            # Check if all chapters are extracted
            if new_extracted_count >= total_chapters and total_chapters > 0:
                updates['status'] = 'text_extracted'
            
            # Apply updates
            transaction.set(book_ref, updates, merge=True)
            
            # Return updated book data
            book_data.update(updates)
            return book_data
        
        # Execute transaction
        transaction = self.db.transaction()
        return update_in_transaction(transaction)
    
    def increment_completed_chapters(self, book_id: str) -> Dict[str, Any]:
        """
        Atomically increment completed chapters counter and check completion.
        Returns the updated book document.
        """
        book_ref = self.db.collection('books-tts').document(book_id)
        
        @firestore.transactional
        def update_in_transaction(transaction):
            # Get current book state
            book_doc = book_ref.get(transaction=transaction)
            if not book_doc.exists:
                raise ValueError(f"Book {book_id} not found")
            
            book_data = book_doc.to_dict()
            
            # Increment completed chapters counter
            new_completed_count = book_data.get('completedChapters', 0) + 1
            total_chapters = book_data.get('chapterCount', 0)
            
            updates = {
                'completedChapters': new_completed_count,
                'updatedAt': firestore.SERVER_TIMESTAMP
            }
            
            # Check if all chapters are completed
            if new_completed_count >= total_chapters and total_chapters > 0:
                updates['status'] = 'completed'
            
            # Apply updates
            transaction.set(book_ref, updates, merge=True)
            
            # Return updated book data
            book_data.update(updates)
            return book_data
        
        # Execute transaction
        transaction = self.db.transaction()
        return update_in_transaction(transaction)
    
    def create_indexes_if_needed(self) -> None:
        """
        Create composite indexes programmatically if they don't exist.
        Note: In practice, indexes should be created via Terraform or gcloud CLI.
        This is just for reference.
        """
        # These would typically be created via Terraform or gcloud commands:
        
        # Collection group chapters: composite index on bookId ASC, order ASC
        # gcloud firestore indexes composite create --collection-group=chapters \
        #   --field-config field-path=bookId,order=ascending \
        #   --field-config field-path=order,order=ascending
        
        # Collection group chapters: composite index on bookId ASC, status ASC
        # gcloud firestore indexes composite create --collection-group=chapters \
        #   --field-config field-path=bookId,order=ascending \
        #   --field-config field-path=status,order=ascending
        
        # Collection group chapters: single-field index on updatedAt DESC
        # gcloud firestore indexes fields create --collection-group=chapters \
        #   --field-path=updatedAt --order=descending
        
        # Top-level books: single-field index on updatedAt DESC  
        # gcloud firestore indexes fields create --collection=books \
        #   --field-path=updatedAt --order=descending
        
        pass


# Utility functions for common operations
def create_error_info(code: str, message: str, timestamp: Optional[datetime] = None) -> Dict[str, Any]:
    """Create error information dictionary"""
    return {
        'code': code,
        'message': message,
        'at': timestamp or datetime.utcnow()
    }


def create_chapter_id(chapter_number: int) -> str:
    """Create standardized chapter ID"""
    return f"chapter_{chapter_number:02d}"


def parse_chapter_id(chapter_id: str) -> Optional[int]:
    """Parse chapter number from chapter ID"""
    try:
        if chapter_id.startswith('chapter_'):
            return int(chapter_id.replace('chapter_', ''))
        return None
    except ValueError:
        return None


# Common status values
class BookStatus:
    NEW = 'new'
    OUTLINE_EXTRACTING = 'outline_extracting'
    OUTLINE_EXTRACTED = 'outline_extracted'
    TEXT_EXTRACTING = 'text_extracting'
    TEXT_EXTRACTED = 'text_extracted'
    TTS_PROCESSING = 'tts_processing'
    COMPLETED = 'completed'
    ERROR = 'error'


class ChapterStatus:
    PENDING_EXTRACTION = 'pending_extraction'
    EXTRACTING = 'extracting'
    EXTRACTED = 'extracted'
    TTS_QUEUED = 'tts_queued'
    TTS_PROCESSING = 'tts_processing'
    COMPLETED = 'completed'
    ERROR = 'error'


# Utility function for generating chapter checksums
def generate_content_checksum(content: str) -> str:
    """Generate SHA256 checksum for chapter content"""
    import hashlib
    return f"sha256:{hashlib.sha256(content.encode('utf-8')).hexdigest()}"