#!/bin/bash
# Build script for Chapter TTS function with git submodule support

set -e

echo "Preparing Chapter TTS function for deployment..."

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "Warning: Not in a git repository, trying parent directory..."
    cd ../..
    if [ ! -d ".git" ]; then
        echo "Error: Cannot find git repository. Make sure you're in the project root."
        exit 1
    fi
fi

# Initialize git submodule if needed
if [ ! -d "functions/chapter-tts/google-voice" ] || [ ! "$(ls -A functions/chapter-tts/google-voice 2>/dev/null)" ]; then
    echo "Initializing google-voice submodule..."
    git submodule update --init --recursive functions/chapter-tts/google-voice
    
    # Verify submodule was initialized correctly
    if [ ! -d "functions/chapter-tts/google-voice" ] || [ ! "$(ls -A functions/chapter-tts/google-voice 2>/dev/null)" ]; then
        echo "Error: Failed to initialize google-voice submodule"
        exit 1
    fi
fi

# Go back to function directory
cd functions/chapter-tts

# Verify submodule has content
if [ ! -f "google-voice/api.py" ]; then
    echo "Error: google-voice submodule is missing required files"
    exit 1
fi

# Clean up build artifacts
rm -rf ./__pycache__
rm -rf ./*.egg-info
rm -rf ./build
rm -rf ./dist

echo "✓ Git submodule ready with content verified"
echo "✓ Build artifacts cleaned"
echo "Ready for Cloud Functions deployment with Docker."