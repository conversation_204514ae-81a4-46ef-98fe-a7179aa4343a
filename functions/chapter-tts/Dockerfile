# Use official Python base image with Debian bookworm
FROM python:3.12-slim-bookworm

# Install ffmpeg and other system dependencies
RUN apt-get update \
    && apt-get install -y \
        ffmpeg \
        git \
        ssh \
        ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Set the working directory in the container
WORKDIR /app

# Copy requirements.txt first for better Docker layer caching
COPY requirements.txt /app/

# Copy the main application files
COPY main.py /app/
COPY *.py /app/

# Copy the google-voice submodule directory if it exists
COPY google-voice/ /app/google-voice/

# Fallback: Clone google-voice if submodule directory is empty or missing critical files
RUN if [ ! -f "google-voice/api.py" ]; then \
        echo "google-voice submodule missing or incomplete, cloning directly..."; \
        rm -rf google-voice && \
        git clone https://bitbucket.org/fonos/google-voice.git google-voice; \
    fi

# Verify google-voice is properly installed
RUN if [ ! -f "google-voice/api.py" ]; then \
        echo "ERROR: google-voice api.py not found after clone"; \
        exit 1; \
    fi

# Install Python dependencies (after google-voice is ready)
RUN pip install --no-cache-dir -r requirements.txt

# Verify the main entry point exists
RUN if [ ! -f "main.py" ]; then \
        echo "ERROR: main.py not found"; \
        exit 1; \
    fi

# Set Python path to include google-voice directory
ENV PYTHONPATH="/app:/app/google-voice:$PYTHONPATH"

# Command to run the application
CMD exec functions-framework --target=process_chapter_tts --port=${PORT:-8080}