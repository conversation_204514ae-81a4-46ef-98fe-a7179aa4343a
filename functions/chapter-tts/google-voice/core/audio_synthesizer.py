"""
Audio synthesis orchestration for Vietnamese audiobook TTS.
"""
from typing import List, Optional
from pydub import AudioSegment
from adapters.base import TTSAdapterInterface
from utils import (
    split_text_by_breaks,
    batcher,
    validate_audio_parameters,
    combine_audio_segments,
    export_audio,
    ProgressTracker,
    ProcessingLogger,
    create_timestamped_log_folder
)
from .segment_processor import SegmentProcessor


class AudioSynthesizer:
    """Orchestrates the complete audio synthesis process with TTS adapter support."""
    
    def __init__(self, tts_adapter: TTSAdapterInterface,
                 enable_logging: bool = True):
        """
        Initialize the audio synthesizer.
        
        Args:
            tts_adapter: TTS adapter for audio synthesis
            enable_logging: Whether to enable sentence-level logging and file saving
        """
        self.tts_adapter = tts_adapter
        self.enable_logging = enable_logging
        self.logger: Optional[ProcessingLogger] = None
        
        # Initialize logger if enabled
        if self.enable_logging:
            self.logger = create_timestamped_log_folder()
        
        self.segment_processor = SegmentProcessor(tts_adapter, self.logger)
    
    def synthesize_text_to_audio(self, text: str, output_filename: str,
                                min_break_duration: int = 1500,
                                max_break_duration: int = 2000,
                                max_workers: int = 10,
                                segment_batch_size: int = 5) -> None:
        """
        Synthesize text to speech by splitting text by [break] tags and inserting silence.
        Processes segments in batches and sentences within each segment in parallel.
        
        Args:
            text: The text to synthesize with pause tags
            output_filename: Path to the output audio file
            min_break_duration: Minimum break duration in milliseconds
            max_break_duration: Maximum break duration in milliseconds
            max_workers: Maximum number of parallel workers for sentence synthesis
            segment_batch_size: Number of segments to process in parallel at a time
            
        Raises:
            ValueError: If parameters are invalid
            Exception: If TTS synthesis fails
        """
        # Validate parameters
        validate_audio_parameters(min_break_duration, max_break_duration,
                                max_workers, segment_batch_size)
        
        # Split text into segments
        text_segments = split_text_by_breaks(text)
        if not text_segments:
            raise ValueError("No valid text segments found in input text")
        
        print(f"Processing {len(text_segments)} text segments...")
        
        # Pre-calculate global sentence indices to maintain order
        global_sentence_indices = []
        if self.logger:
            current_global_index = 0
            for segment in text_segments:
                global_sentence_indices.append(current_global_index)
                # Count sentences in this segment
                from utils import split_segment_by_punctuation
                sentences = split_segment_by_punctuation(segment.strip())
                sentence_count = max(1, len(sentences))  # At least 1 for empty/single segments
                current_global_index += sentence_count
        
        # Initialize progress tracking
        progress = ProgressTracker(len(text_segments))
        segment_results = []
        
        # Process segments in batches
        processed_segments = 0
        for batch_num, segment_batch in enumerate(batcher(text_segments, segment_batch_size)):
            print(f"Processing batch {batch_num + 1} with {len(segment_batch)} segments...")
            
            # Get global sentence indices for this batch
            batch_global_indices = None
            if self.logger:
                batch_start = processed_segments
                batch_end = processed_segments + len(segment_batch)
                batch_global_indices = global_sentence_indices[batch_start:batch_end]
            
            # Process the batch
            batch_audio_segments = self.segment_processor.process_segments_batch(
                list(segment_batch), max_workers, processed_segments, batch_global_indices
            )
            
            # Add to results
            segment_results.extend(batch_audio_segments)
            processed_segments += len(segment_batch)
            
            # Update progress
            progress.update(len(segment_batch))
            print(f"Progress: {progress}")
        
        # Combine all audio segments with breaks
        print("Combining audio segments with variable breaks...")
        combined_audio = combine_audio_segments(
            segment_results, min_break_duration, max_break_duration
        )
        
        # Export the final audio
        export_audio(combined_audio, output_filename)
        
        # Save sentences list and create session summary if logging is enabled
        if self.logger:
            self.logger.save_sentences_list()
            processing_stats = {
                "total_segments": len(text_segments),
                "processed_segments": len(segment_results),
                "final_audio_duration_minutes": len(combined_audio) / 60000.0,
                "output_file": output_filename
            }
            self.logger.create_session_summary(processing_stats)
            
            # Print logging information
            session_info = self.logger.get_session_info()
            print(f"\n=== Logging Information ===")
            print(f"Session folder: {session_info['session_dir']}")
            print(f"Individual audio files: {session_info['audio_dir']}")
            print(f"Sentences list: {session_info['sentences_file']}")
            print(f"Total sentences logged: {session_info['total_sentences']}")
        
        # Print final statistics
        self._print_synthesis_statistics(combined_audio, len(text_segments), len(segment_results))
    
    def _print_synthesis_statistics(self, audio: AudioSegment, 
                                  total_segments: int, processed_segments: int) -> None:
        """
        Print statistics about the synthesis process.
        
        Args:
            audio: Final combined audio
            total_segments: Total number of text segments
            processed_segments: Number of successfully processed segments
        """
        duration_seconds = len(audio) / 1000.0
        duration_minutes = duration_seconds / 60.0
        
        print(f"\n=== Synthesis Complete ===")
        print(f"Total segments processed: {processed_segments}/{total_segments}")
        print(f"Final audio duration: {duration_minutes:.2f} minutes ({duration_seconds:.1f} seconds)")
        print(f"Audio size: {len(audio)} samples")
        
        if processed_segments < total_segments:
            print(f"Warning: {total_segments - processed_segments} segments failed to process")
    
    def preview_synthesis(self, text: str, max_segments: int = 3) -> dict:
        """
        Preview what the synthesis process would produce without actually synthesizing.
        
        Args:
            text: Text to preview
            max_segments: Maximum number of segments to preview
            
        Returns:
            Dictionary containing preview information
        """
        text_segments = split_text_by_breaks(text)
        
        preview_info = {
            'total_segments': len(text_segments),
            'preview_segments': [],
            'estimated_duration_minutes': 0
        }
        
        # Preview first few segments
        for i, segment in enumerate(text_segments[:max_segments]):
            segment_stats = self.segment_processor.get_segment_statistics(segment)
            preview_info['preview_segments'].append({
                'index': i + 1,
                'text_preview': segment[:100] + "..." if len(segment) > 100 else segment,
                'statistics': segment_stats
            })
            preview_info['estimated_duration_minutes'] += segment_stats.get('estimated_synthesis_time', 0) / 60
        
        # Estimate total duration
        if text_segments:
            avg_segment_time = preview_info['estimated_duration_minutes'] / min(len(text_segments), max_segments)
            preview_info['estimated_total_duration_minutes'] = avg_segment_time * len(text_segments)
        
        return preview_info
    
    def validate_synthesis_input(self, text: str) -> List[str]:
        """
        Validate input text for synthesis and return any issues found.
        
        Args:
            text: Text to validate
            
        Returns:
            List of validation issues (empty if no issues)
        """
        issues = []
        
        if not text or not text.strip():
            issues.append("Input text is empty")
            return issues
        
        # Check for break tags
        if '[break]' not in text:
            issues.append("No [break] tags found - audio will be one continuous segment")
        
        # Check segments
        segments = split_text_by_breaks(text)
        if not segments:
            issues.append("No valid text segments found after splitting by [break] tags")
        
        # Check individual segments
        for i, segment in enumerate(segments):
            if not self.segment_processor.validate_segment(segment):
                issues.append(f"Segment {i+1} is invalid or too short/long")
        
        return issues