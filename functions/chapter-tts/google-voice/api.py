"""
Clean API interface for Vietnamese Audiobook TTS processing.

This module provides a simple function interface for the book-text-to-speech orchestrator
while keeping all existing functionality intact.
"""
import os
import tempfile
from typing import Union, Optional, Dict, Any
from pydub import AudioSegment

from config.settings import Settings
from services import FileService, GeminiService
from core import TextProcessor, AudioSynthesizer
from adapters import TTSAdapterFactory
from utils import validate_text_input, clean_text_for_tts


def process_text_to_audio(
    text: str,
    config: Optional[Dict[str, Any]] = None,
    return_bytes: bool = False
) -> Union[str, bytes]:
    """
    Process Vietnamese text to audio using AI-powered tagging and Google Cloud TTS.
    
    This is the main API function for the library, designed to be called by external
    orchestrators like the book-text-to-speech Firebase Functions.
    
    Args:
        text: Raw Vietnamese text content to be converted to speech
        config: Optional configuration overrides. Can include:
            - gemini_api_key: Gemini API key (overrides config.ini)
            - min_break: Minimum break duration in milliseconds (default: 1500)
            - max_break: Maximum break duration in milliseconds (default: 2000)
            - max_workers: Maximum parallel workers for synthesis (default: 10)
            - segment_batch_size: Segments to process in parallel (default: 5)
            - tts_engine: TTS engine to use ('google', 'elevenlabs', 'openai')
            - tts_config: TTS configuration dict
        return_bytes: If True, return audio as bytes; if False, return temp file path
        
    Returns:
        - If return_bytes=True: bytes object containing WAV audio data
        - If return_bytes=False: str path to temporary audio file
        
    Raises:
        ValueError: If text is invalid or configuration is incorrect
        Exception: If processing or TTS synthesis fails
        
    Example:
        # Return audio bytes for GCS upload
        audio_data = process_text_to_audio(
            text="Xin chào! Đây là chương đầu tiên.",
            config={"gemini_api_key": "your_key", "tts_engine": "google"},
            return_bytes=True
        )
        
        # Return temp file path
        audio_path = process_text_to_audio(
            text="Xin chào! Đây là chương đầu tiên.",
            config={"gemini_api_key": "your_key"},
            return_bytes=False
        )
    """
    # Validate input text
    if not text or not text.strip():
        raise ValueError("Input text cannot be empty")
    
    if not validate_text_input(text):
        raise ValueError("Input text contains no valid content")
    
    # Initialize configuration with overrides
    config = config or {}
    settings = Settings(config_overrides=config)
    
    try:
        # Initialize services
        file_service = FileService()
        
        # Get API key from settings (handles overrides internally)
        api_key = settings.get_gemini_api_key()
        gemini_service = GeminiService(api_key)
        
        # Get TTS config and engine (handles overrides internally)
        tts_engine = settings.get_tts_engine()
        tts_config = settings.get_tts_config()
        tts_adapter = TTSAdapterFactory.create_adapter(tts_engine, tts_config)
        
        # Initialize processors
        text_processor = TextProcessor(gemini_service, file_service, tts_adapter)
        audio_synthesizer = AudioSynthesizer(tts_adapter)
        
        # Process text (add pause tags)
        cleaned_text = clean_text_for_tts(text)
        tagged_text = gemini_service.generate_tagged_text(cleaned_text)
        
        # Get audio processing parameters
        min_break = config.get("min_break", 1500)
        max_break = config.get("max_break", 2000) 
        max_workers = config.get("max_workers", 10)
        segment_batch_size = config.get("segment_batch_size", 5)
        
        # Create temporary output file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
            temp_audio_path = temp_audio.name
        
        # Synthesize audio
        audio_synthesizer.synthesize_text_to_audio(
            text=tagged_text,
            output_filename=temp_audio_path,
            min_break_duration=min_break,
            max_break_duration=max_break,
            max_workers=max_workers,
            segment_batch_size=segment_batch_size
        )
        
        # Return based on requested format
        if return_bytes:
            # Load audio file and return bytes
            audio_segment = AudioSegment.from_wav(temp_audio_path)
            audio_bytes = audio_segment.export(format="wav").read()
            
            # Clean up temp file
            os.unlink(temp_audio_path)
            return audio_bytes
        else:
            # Return temp file path (caller responsible for cleanup)
            return temp_audio_path
            
    except Exception as e:
        raise e


