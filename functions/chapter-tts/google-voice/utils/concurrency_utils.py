"""
Concurrency utilities for parallel processing in Vietnamese audiobook TTS.
"""
from itertools import islice
from typing import Itera<PERSON>, <PERSON><PERSON>, TypeVar, Iterable

T = TypeVar('T')


def batcher(iterable: Iterable[T], n: int) -> Iterator[Tuple[T, ...]]:
    """
    Batches an iterable into chunks of size n.
    
    Args:
        iterable: The iterable to batch
        n: Size of each batch
        
    Yields:
        Tuples containing batched items
    """
    it = iter(iterable)
    while True:
        chunk = tuple(islice(it, n))
        if not chunk:
            return
        yield chunk


def validate_concurrency_parameters(max_workers: int, batch_size: int) -> None:
    """
    Validate concurrency parameters.
    
    Args:
        max_workers: Maximum number of parallel workers
        batch_size: Size of processing batches
        
    Raises:
        ValueError: If parameters are invalid
    """
    if max_workers < 1:
        raise ValueError("max_workers must be at least 1")
    if batch_size < 1:
        raise ValueError("batch_size must be at least 1")


def calculate_optimal_batch_size(total_items: int, max_workers: int) -> int:
    """
    Calculate optimal batch size based on total items and available workers.
    
    Args:
        total_items: Total number of items to process
        max_workers: Maximum number of parallel workers
        
    Returns:
        Optimal batch size
    """
    if total_items <= max_workers:
        return 1
    
    # Aim for batches that utilize all workers efficiently
    optimal_size = max(1, total_items // max_workers)
    
    # Ensure we don't create too many small batches
    if optimal_size < 3 and total_items > max_workers * 3:
        optimal_size = 3
    
    return optimal_size


class ProgressTracker:
    """Simple progress tracker for batch processing."""
    
    def __init__(self, total_items: int):
        """
        Initialize progress tracker.
        
        Args:
            total_items: Total number of items to process
        """
        self.total_items = total_items
        self.completed_items = 0
    
    def update(self, completed_count: int = 1) -> None:
        """
        Update progress counter.
        
        Args:
            completed_count: Number of items completed
        """
        self.completed_items += completed_count
    
    def get_progress_percentage(self) -> float:
        """
        Get current progress as percentage.
        
        Returns:
            Progress percentage (0.0 to 100.0)
        """
        if self.total_items == 0:
            return 100.0
        return (self.completed_items / self.total_items) * 100.0
    
    def is_complete(self) -> bool:
        """
        Check if processing is complete.
        
        Returns:
            True if all items are processed
        """
        return self.completed_items >= self.total_items
    
    def __str__(self) -> str:
        """String representation of progress."""
        return f"{self.completed_items}/{self.total_items} ({self.get_progress_percentage():.1f}%)"