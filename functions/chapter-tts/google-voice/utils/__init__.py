"""
Utility modules for Vietnamese audiobook TTS processing.
"""

from .text_utils import (
    split_segment_by_punctuation,
    split_text_by_breaks,
    validate_text_input,
    clean_text_for_tts
)

from .audio_utils import (
    create_variable_break_pause,
    combine_audio_segments,
    export_audio,
    validate_audio_parameters,
    get_audio_duration_info
)

from .concurrency_utils import (
    batcher,
    validate_concurrency_parameters,
    calculate_optimal_batch_size,
    ProgressTracker
)

from .logging_utils import (
    ProcessingLogger,
    create_timestamped_log_folder
)

__all__ = [
    # Text utilities
    'split_segment_by_punctuation',
    'split_text_by_breaks',
    'validate_text_input',
    'clean_text_for_tts',
    
    # Audio utilities
    'create_variable_break_pause',
    'combine_audio_segments',
    'export_audio',
    'validate_audio_parameters',
    'get_audio_duration_info',
    
    # Concurrency utilities
    'batcher',
    'validate_concurrency_parameters',
    'calculate_optimal_batch_size',
    'ProgressTracker',
    
    # Logging utilities
    'ProcessingLogger',
    'create_timestamped_log_folder'
]