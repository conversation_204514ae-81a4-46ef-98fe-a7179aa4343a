"""
Command-line argument parsing for Vietnamese audiobook TTS processor.
"""
import argparse
from argparse import Namespace


class ArgumentParser:
    """Handles command-line argument parsing."""
    
    def __init__(self):
        """Initialize the argument parser."""
        self.parser = self._create_parser()
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """
        Create and configure the argument parser.
        
        Returns:
            Configured ArgumentParser instance
        """
        parser = argparse.ArgumentParser(
            description="Process a Vietnamese audiobook chapter for TTS with pause annotations.",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  %(prog)s input.txt tagged.txt output.wav
  %(prog)s input.txt tagged.txt output.wav --force-tagging
  %(prog)s input.txt tagged.txt output.wav --max-workers 20 --segment-batch-size 10
            """
        )
        
        # Required arguments
        parser.add_argument(
            "input_file",
            help="The path to the input text file (used if tagged file doesn't exist)."
        )
        
        parser.add_argument(
            "tagged_file",
            help="The path to the output tagged text file."
        )
        
        parser.add_argument(
            "output_file",
            help="The path to the local output audio file (e.g., chapter.wav)."
        )
        
        # Optional flags
        parser.add_argument(
            "--force-tagging",
            action="store_true",
            help="Force the script to re-tag the text even if it already contains tags."
        )
        
        # Audio processing parameters
        audio_group = parser.add_argument_group('Audio Processing')
        audio_group.add_argument(
            "--min-break",
            type=int,
            default=1500,
            help="Minimum break duration in milliseconds (default: 1500)"
        )
        
        audio_group.add_argument(
            "--max-break",
            type=int,
            default=2000,
            help="Maximum break duration in milliseconds (default: 2000)"
        )
        
        # Performance parameters
        perf_group = parser.add_argument_group('Performance')
        perf_group.add_argument(
            "--max-workers",
            type=int,
            default=10,
            help="Maximum number of parallel workers for sentence synthesis (default: 10)"
        )
        
        perf_group.add_argument(
            "--segment-batch-size",
            type=int,
            default=5,
            help="Number of segments to process in parallel at a time (default: 5)"
        )
        
        # Configuration options
        config_group = parser.add_argument_group('Configuration')
        config_group.add_argument(
            "--config",
            default="config.ini",
            help="Path to configuration file (default: config.ini)"
        )
        
        config_group.add_argument(
            "--tts-engine",
            choices=['google', 'elevenlabs', 'openai'],
            help="TTS engine to use (overrides config file setting)"
        )
        
        # Output options
        output_group = parser.add_argument_group('Output')
        output_group.add_argument(
            "--preview",
            action="store_true",
            help="Preview the synthesis process without actually generating audio"
        )
        
        output_group.add_argument(
            "--validate-only",
            action="store_true",
            help="Only validate the input text without processing"
        )
        
        output_group.add_argument(
            "--verbose",
            action="store_true",
            help="Enable verbose output"
        )
        
        return parser
    
    def parse_args(self, args=None) -> Namespace:
        """
        Parse command-line arguments.
        
        Args:
            args: List of arguments to parse (None for sys.argv)
            
        Returns:
            Parsed arguments namespace
        """
        return self.parser.parse_args(args)
    
    def validate_args(self, args: Namespace) -> list:
        """
        Validate parsed arguments and return any issues.
        
        Args:
            args: Parsed arguments namespace
            
        Returns:
            List of validation issues (empty if no issues)
        """
        issues = []
        
        # Validate break duration parameters
        if args.min_break < 0:
            issues.append("min-break must be non-negative")
        
        if args.max_break < 0:
            issues.append("max-break must be non-negative")
        
        if args.min_break >= args.max_break:
            issues.append("min-break must be less than max-break")
        
        # Validate performance parameters
        if args.max_workers < 1:
            issues.append("max-workers must be at least 1")
        
        if args.segment_batch_size < 1:
            issues.append("segment-batch-size must be at least 1")
        
        # Validate file extensions
        if not args.output_file.lower().endswith(('.wav', '.mp3')):
            issues.append("output file should have .wav or .mp3 extension")
        
        return issues
    
    def print_help(self):
        """Print help message."""
        self.parser.print_help()
    
    def get_usage(self) -> str:
        """
        Get usage string.
        
        Returns:
            Usage string
        """
        return self.parser.format_usage()