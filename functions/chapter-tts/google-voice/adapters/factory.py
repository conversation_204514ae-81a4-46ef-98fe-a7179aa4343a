"""
Factory pattern implementation for TTS adapters.

This module provides the factory for creating TTS adapter instances
based on configuration settings.
"""

from typing import Dict, Any, Type
from .base import TTSAdapterInterface
from .google_tts import GoogleTTSAdapter
from .elevenlabs_tts import ElevenLabsAdapter
from .openai_tts import OpenAITTSAdapter


class TTSAdapterFactory:
    """Factory for creating TTS adapter instances."""
    
    # Registry of available TTS adapters
    _adapters: Dict[str, Type[TTSAdapterInterface]] = {
        'google': GoogleTTSAdapter,
        'google_tts': GoogleTTSAdapter,
        'elevenlabs': ElevenLabsAdapter,
        'openai': OpenAITTSAdapter,
        'openai_tts': OpenAITTSAdapter,
    }
    
    @classmethod
    def create_adapter(cls, engine_name: str, config: Dict[str, Any]) -> TTSAdapterInterface:
        """
        Create a TTS adapter instance for the specified engine.
        
        Args:
            engine_name: Name of the TTS engine
            config: Configuration dictionary for the adapter
            
        Returns:
            TTS adapter instance
            
        Raises:
            ValueError: If engine is not supported
        """
        engine_name = engine_name.lower()
        
        if engine_name not in cls._adapters:
            available_engines = list(cls._adapters.keys())
            raise ValueError(
                f"Unsupported TTS engine: '{engine_name}'. "
                f"Available engines: {available_engines}"
            )
        
        adapter_class = cls._adapters[engine_name]
        return adapter_class(config)
    
    @classmethod
    def register_adapter(cls, engine_name: str, adapter_class: Type[TTSAdapterInterface]) -> None:
        """
        Register a new TTS adapter.
        
        Args:
            engine_name: Name of the TTS engine
            adapter_class: Adapter class to register
        """
        cls._adapters[engine_name.lower()] = adapter_class
    
    @classmethod
    def get_available_engines(cls) -> list:
        """
        Get list of available TTS engines.
        
        Returns:
            List of available engine names
        """
        return list(cls._adapters.keys())
    
    @classmethod
    def is_engine_supported(cls, engine_name: str) -> bool:
        """
        Check if a TTS engine is supported.
        
        Args:
            engine_name: Name of the TTS engine
            
        Returns:
            True if engine is supported, False otherwise
        """
        return engine_name.lower() in cls._adapters