"""
OpenAI Text-to-Speech adapter implementation.

This module provides the OpenAI TTS adapter that implements the TTSAdapterInterface
for OpenAI TTS services.
"""

import time
import io
from typing import Dict, Any, Tuple, List
from pydub import AudioSegment
import openai

from .base import BaseTTSAdapter, PromptTemplateInterface


class OpenAIPromptTemplate(PromptTemplateInterface):
    """Prompt template for OpenAI TTS with natural text formatting."""
    
    def get_tagging_prompt(self, text: str) -> str:
        """
        Generate an OpenAI TTS-specific prompt for text tagging.
        
        Args:
            text: Input text to process
            
        Returns:
            Formatted prompt for OpenAI TTS
        """
        return f"""
Please analyze the following Vietnamese text and prepare it for OpenAI text-to-speech synthesis.

Instructions:
1.  Use `[break]` for significant pauses between paragraphs. This is the most important instruction.
2.  Use natural punctuation for pauses (commas, periods, semicolons) - OpenAI TTS handles natural speech well.
3.  OpenAI TTS works best with clean, natural text without excessive markup.
4.  Convert all numbers to their Vietnamese text representation. For example, "29.000 đô-la" should be converted to "hai mươi chín nghìn đô-la".
5.  Convert all metric units to their full Vietnamese representation. For example, "6 kg" should be converted to "sáu ký-lô-gam", "10 cm" to "mười cen-ti-mét", and "5m" to "năm mét".
6.  Convert English names to a TTS-friendly phonetic representation. For example, "Phineas" should be converted to "Finias".
7.  Remove unnecessary punctuation within names, but preserve sentence-level punctuation. For example, "Bác sĩ J. M. Harlow" should become "Bác sĩ J M Harlow", but the period at the end of a sentence should be kept.
8.  Remove special characters that should not be read aloud, such as quotation marks ("").
9.  Convert all text to lowercase, except for proper nouns (e.g., names of people, places, companies).
10. Do not add XML or SSML tags - OpenAI TTS works best with natural text.
11. Return only the modified text with the inserted tags. Do not include any explanations or introductory text in your response.
12. Crucially, do not alter, add, or remove any of the original text, other than the number to text and case conversions.
13. Strictly preserve the period (.) at the end of every sentence.

Here is the text to process:
---
{text}
"""
    
    def get_supported_tags(self) -> List[str]:
        """
        Get list of supported pause/break tags for OpenAI TTS.
        
        Returns:
            List of supported tag names
        """
        return ['[break]']  # OpenAI TTS uses minimal markup
    
    def validate_tagged_text(self, text: str) -> bool:
        """
        Validate that tagged text contains proper formatting for OpenAI TTS.
        
        Args:
            text: Tagged text to validate
            
        Returns:
            True if text is properly formatted for OpenAI TTS
        """
        if not text or not text.strip():
            return False
        
        # OpenAI TTS is flexible, just check for basic content
        return len(text.strip()) > 0


class OpenAITTSAdapter(BaseTTSAdapter):
    """OpenAI Text-to-Speech adapter implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the OpenAI TTS adapter.
        
        Args:
            config: OpenAI TTS configuration dictionary
        """
        super().__init__(config)
        openai.api_key = self.config.get('api_key', '')
        self.client = openai.OpenAI(api_key=self.config.get('api_key', ''))
    
    def _validate_required_config(self) -> None:
        """Validate that required configuration keys are present."""
        required_keys = ['api_key']
        for key in required_keys:
            if key not in self.config or not self.config[key]:
                raise ValueError(f"Required configuration key '{key}' not found for OpenAI TTS")
    
    def _create_prompt_template(self) -> PromptTemplateInterface:
        """
        Create the prompt template for OpenAI TTS.
        
        Returns:
            OpenAI TTS prompt template instance
        """
        return OpenAIPromptTemplate()
    
    def synthesize_speech(self, text: str, retries: int = 5) -> bytes:
        """
        Synthesize speech from text with retry logic.
        
        Args:
            text: Text to synthesize
            retries: Number of retry attempts
            
        Returns:
            Audio data as bytes
            
        Raises:
            Exception: If synthesis fails after all retries
        """
        for i in range(retries):
            try:
                response = self.client.audio.speech.create(
                    model=self.config.get('model', 'tts-1'),
                    voice=self.config.get('voice', 'alloy'),
                    input=text,
                    speed=self.config.get('speed', 1.0)
                )
                return response.content
                
            except Exception as e:
                if "rate_limit" in str(e).lower() or "429" in str(e):
                    wait_time = 2 ** i  # Exponential backoff
                    print(f"  Rate limit hit. Retrying in {wait_time} seconds... (Attempt {i+1}/{retries})")
                    time.sleep(wait_time)
                else:
                    self._handle_synthesis_error(e, text, i+1, retries)
        
        raise Exception(f"Failed to synthesize speech after {retries} retries.")
    
    def synthesize_to_audio_segment(self, text: str) -> AudioSegment:
        """
        Synthesize text to AudioSegment.
        
        Args:
            text: Text to synthesize
            
        Returns:
            AudioSegment containing the synthesized audio
        """
        audio_content = self.synthesize_speech(text)
        return AudioSegment.from_mp3(io.BytesIO(audio_content))
    
    def synthesize_sentence(self, sentence: str, sentence_index: int) -> Tuple[int, AudioSegment]:
        """
        Synthesize a single sentence to audio.
        
        Args:
            sentence: Sentence to synthesize
            sentence_index: Index of the sentence for ordering
            
        Returns:
            Tuple of (sentence_index, AudioSegment) for maintaining order
        """
        print(f"  Synthesizing sentence {sentence_index + 1}: \"{sentence}\"")
        audio_segment = self.synthesize_to_audio_segment(sentence)
        return (sentence_index, audio_segment)
    
    def get_available_voices(self) -> List[str]:
        """
        Get list of available voices.
        
        Returns:
            List of available voice names
        """
        # OpenAI TTS has predefined voices
        return ['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer']
    
    def validate_config(self) -> bool:
        """
        Validate that the adapter configuration is valid.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Check if required config is present
            self._validate_required_config()
            
            # Test API connection with a simple request
            test_response = self.client.audio.speech.create(
                model=self.config.get('model', 'tts-1'),
                voice=self.config.get('voice', 'alloy'),
                input="test",
                speed=self.config.get('speed', 1.0)
            )
            
            # If we get here without exception, the config is valid
            return True
            
        except Exception as e:
            print(f"Configuration validation failed: {e}")
            return False
    
    def get_engine_name(self) -> str:
        """
        Get the name of this TTS engine.
        
        Returns:
            Engine name string
        """
        return "openai"