"""
Google Cloud Text-to-Speech adapter implementation.

This module provides the Google TTS adapter that implements the TTSAdapterInterface
for Google Cloud Text-to-Speech services.
"""

import time
import io
from typing import Dict, Any, Tuple, List
from google.cloud import texttospeech_v1
from pydub import AudioSegment

from .base import BaseTTSAdapter, PromptTemplateInterface


class GoogleTTSPromptTemplate(PromptTemplateInterface):
    """Prompt template for Google Cloud TTS with custom pause tags."""
    
    def get_tagging_prompt(self, text: str) -> str:
        """
        Generate a Google TTS-specific prompt for text tagging.
        
        Args:
            text: Input text to process
            
        Returns:
            Formatted prompt for Google TTS
        """
        return f"""
Please analyze the following Vietnamese text and insert pause and break tags to create natural-sounding pauses for a text-to-speech (TTS) engine.

Instructions:
1.  Use `[break]` for significant pauses between paragraphs. This is the most important instruction.
2.  Place pause tags immediately after the punctuation mark, not before.
3.  Use `[pause long]` for long pauses within a paragraph.
4.  Use `[pause]` for standard sentence breaks. When a hyphen connects two parts of a sentence, replace it with a `[pause]` tag.
5.  Use `[pause short]` for brief pauses to improve prosody.
6.  Convert all numbers to their Vietnamese text representation. For example, "29.000 đô-la" should be converted to "hai mươi chín nghìn đô-la".
7.  Convert all metric units to their full Vietnamese representation. For example, "6 kg" should be converted to "sáu ký-lô-gam", "10 cm" to "mười cen-ti-mét", and "5m" to "năm mét".
8.  Convert English names to a TTS-friendly phonetic representation. For example, "Phineas" should be converted to "Finias".
9.  Remove unnecessary punctuation within names, but preserve sentence-level punctuation. For example, "Bác sĩ J. M. Harlow" should become "Bác sĩ J M Harlow", but the period at the end of a sentence should be kept.
10. Remove special characters that should not be read aloud, such as quotation marks ("").
11. Convert all text to lowercase, except for proper nouns (e.g., names of people, places, companies).
12. Do not add any other XML or SSML tags.
13. Return only the modified text with the inserted tags. Do not include any explanations or introductory text in your response.
14. Crucially, do not alter, add, or remove any of the original text, other than the number to text and case conversions.
15. Strictly preserve the period (.) at the end of every sentence.

Here is the text to process:
---
{text}
"""
    
    def get_supported_tags(self) -> List[str]:
        """
        Get list of supported pause/break tags for Google TTS.
        
        Returns:
            List of supported tag names
        """
        return ['[break]', '[pause]', '[pause short]', '[pause long]']
    
    def validate_tagged_text(self, text: str) -> bool:
        """
        Validate that tagged text contains proper formatting for Google TTS.
        
        Args:
            text: Tagged text to validate
            
        Returns:
            True if text is properly formatted for Google TTS
        """
        if not text or not text.strip():
            return False
        
        # Check for presence of break tags (most important)
        has_breaks = '[break]' in text
        
        # Check for other pause tags
        has_pauses = any(tag in text for tag in ['[pause]', '[pause short]', '[pause long]'])
        
        return has_breaks or has_pauses


class GoogleTTSAdapter(BaseTTSAdapter):
    """Google Cloud Text-to-Speech adapter implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Google TTS adapter.
        
        Args:
            config: Google TTS configuration dictionary
        """
        super().__init__(config)
        self.client = texttospeech_v1.TextToSpeechClient()
        self.voice = self._create_voice_config()
        self.audio_config = self._create_audio_config()
    
    def _validate_required_config(self) -> None:
        """Validate that required configuration keys are present."""
        required_keys = ['language_code']
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"Required configuration key '{key}' not found for Google TTS")
    
    def _create_prompt_template(self) -> PromptTemplateInterface:
        """
        Create the prompt template for Google TTS.
        
        Returns:
            Google TTS prompt template instance
        """
        return GoogleTTSPromptTemplate()
    
    def _create_voice_config(self) -> texttospeech_v1.VoiceSelectionParams:
        """
        Create voice selection parameters.
        
        Returns:
            Voice selection parameters
        """
        return texttospeech_v1.VoiceSelectionParams(
            language_code=self.config.get('language_code', 'vi-VN'),
            name=self.config.get('voice_name', 'vi-VN-Chirp3-HD-Algenib')
        )
    
    def _create_audio_config(self) -> texttospeech_v1.AudioConfig:
        """
        Create audio configuration.
        
        Returns:
            Audio configuration
        """
        # Map string encoding to enum
        encoding_map = {
            'LINEAR16': texttospeech_v1.AudioEncoding.LINEAR16,
            'MP3': texttospeech_v1.AudioEncoding.MP3,
            'OGG_OPUS': texttospeech_v1.AudioEncoding.OGG_OPUS
        }
        
        encoding_str = self.config.get('audio_encoding', 'LINEAR16')
        audio_encoding = encoding_map.get(encoding_str, texttospeech_v1.AudioEncoding.LINEAR16)
        
        return texttospeech_v1.AudioConfig(
            audio_encoding=audio_encoding,
            speaking_rate=self.config.get('speaking_rate', 0.99)
        )
    
    def synthesize_speech(self, text: str, retries: int = 5) -> bytes:
        """
        Synthesize speech from text with retry logic.
        
        Args:
            text: Text to synthesize
            retries: Number of retry attempts
            
        Returns:
            Audio data as bytes
            
        Raises:
            Exception: If synthesis fails after all retries
        """
        synthesis_input = texttospeech_v1.SynthesisInput(markup=text)
        backoff_in_seconds = 15
        
        for i in range(retries):
            try:
                response = self.client.synthesize_speech(
                    input=synthesis_input, 
                    voice=self.voice, 
                    audio_config=self.audio_config
                )
                return response.audio_content
            except Exception as e:
                if "429" in str(e):
                    print(f"  Rate limit hit. Retrying in {backoff_in_seconds} seconds... (Attempt {i+1}/{retries})")
                    time.sleep(backoff_in_seconds)
                    backoff_in_seconds *= 2  # Exponential backoff
                else:
                    self._handle_synthesis_error(e, text, i+1, retries)
        
        raise Exception(f"Failed to synthesize speech after {retries} retries.")
    
    def synthesize_to_audio_segment(self, text: str) -> AudioSegment:
        """
        Synthesize text to AudioSegment.
        
        Args:
            text: Text to synthesize
            
        Returns:
            AudioSegment containing the synthesized audio
        """
        audio_content = self.synthesize_speech(text)
        return AudioSegment.from_wav(io.BytesIO(audio_content))
    
    def synthesize_sentence(self, sentence: str, sentence_index: int) -> Tuple[int, AudioSegment]:
        """
        Synthesize a single sentence to audio.
        
        Args:
            sentence: Sentence to synthesize
            sentence_index: Index of the sentence for ordering
            
        Returns:
            Tuple of (sentence_index, AudioSegment) for maintaining order
        """
        print(f"  Synthesizing sentence {sentence_index + 1}: \"{sentence}\"")
        audio_segment = self.synthesize_to_audio_segment(sentence)
        return (sentence_index, audio_segment)
    
    def get_available_voices(self) -> List[str]:
        """
        Get list of available voices for the configured language.
        
        Returns:
            List of available voice names
        """
        try:
            voices = self.client.list_voices()
            language_code = self.config.get('language_code', 'vi-VN')
            
            available_voices = []
            for voice in voices.voices:
                if language_code in voice.language_codes:
                    available_voices.append(voice.name)
            
            return available_voices
        except Exception as e:
            print(f"Error getting available voices: {e}")
            return []
    
    def validate_config(self) -> bool:
        """
        Validate that the adapter configuration is valid.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Check if required config is present
            self._validate_required_config()
            
            # Check if voice is available
            available_voices = self.get_available_voices()
            voice_name = self.config.get('voice_name')
            
            if voice_name and voice_name not in available_voices:
                print(f"Warning: Voice '{voice_name}' not found in available voices")
                return False
            
            return True
        except Exception as e:
            print(f"Configuration validation failed: {e}")
            return False
    
    def get_engine_name(self) -> str:
        """
        Get the name of this TTS engine.
        
        Returns:
            Engine name string
        """
        return "google_tts"