"""
Main entry point for the Vietnamese Audiobook TTS Processor.

This modular application processes Vietnamese text for audiobook creation,
adding pause tags using Gemini AI and synthesizing speech using Google Cloud TTS.
"""
import sys

from cli import ArgumentParser
from config.settings import Settings
from services import FileService, GeminiService
from core import TextProcessor, AudioSynthesizer
from adapters import TTSAdapterFactory


class AudiobookProcessor:
    """Main application orchestrator."""
    
    def __init__(self, config_file: str = 'config.ini'):
        """
        Initialize the audiobook processor.
        
        Args:
            config_file: Path to configuration file
        """
        try:
            self.settings = Settings(config_file)
            self.file_service = FileService()
            self._initialize_services()
        except Exception as e:
            print(f"Error initializing application: {e}")
            sys.exit(1)
    
    def _initialize_services(self, tts_engine_override: str = None):
        """Initialize all services and processors."""
        # Initialize API services (only if not already initialized)
        if not hasattr(self, 'gemini_service'):
            api_key = self.settings.get_gemini_api_key()
            self.gemini_service = GeminiService(api_key)
        
        # Initialize TTS adapter based on configuration or override
        tts_engine = tts_engine_override or self.settings.get_tts_engine()
        tts_config = self.settings.get_tts_config()
        
        # Check if we need to reinitialize TTS adapter
        current_engine = getattr(self, '_current_tts_engine', None)
        if current_engine != tts_engine or not hasattr(self, 'tts_adapter'):
            try:
                self.tts_adapter = TTSAdapterFactory.create_adapter(tts_engine, tts_config)
                self._current_tts_engine = tts_engine
            except Exception as e:
                print(f"Error initializing TTS adapter '{tts_engine}': {e}")
                print("Falling back to google_tts adapter...")
                self.tts_adapter = TTSAdapterFactory.create_adapter('google_tts', tts_config)
                self._current_tts_engine = 'google_tts'
        
        # Initialize core processors
        self.text_processor = TextProcessor(self.gemini_service, self.file_service, self.tts_adapter)
        self.audio_synthesizer = AudioSynthesizer(self.tts_adapter)
    
    def validate_inputs(self, input_file: str, tagged_file: str, output_file: str) -> bool:
        """
        Validate all input parameters and files.
        
        Args:
            input_file: Path to input text file
            tagged_file: Path to tagged output file
            output_file: Path to output audio file
            
        Returns:
            True if all validations pass, False otherwise
        """
        # Validate input file
        if not self.file_service.validate_file(input_file, must_exist=True):
            return False
        
        # Validate output directories
        if not self.file_service.validate_output_path(tagged_file):
            return False
            
        if not self.file_service.validate_output_path(output_file):
            return False
        
        # Validate audio format
        if not output_file.lower().endswith(('.wav', '.mp3')):
            print("Error: Output file must have .wav or .mp3 extension")
            return False
        
        return True
    
    def process_audiobook(
        self, 
        input_file: str, 
        tagged_file: str, 
        output_file: str,
        force_tagging: bool = False,
        preview_only: bool = False,
        validate_only: bool = False,
        tts_engine: str = None
    ) -> bool:
        """
        Process the audiobook from start to finish.
        
        Args:
            input_file: Path to input text file
            tagged_file: Path to output tagged text file
            output_file: Path to output audio file
            force_tagging: Force re-tagging even if tagged file exists
            preview_only: Only show processing preview without generating audio
            validate_only: Only validate inputs without processing
            tts_engine: TTS engine to use (overrides config)
            
        Returns:
            True if processing completed successfully, False otherwise
        """
        # Validate inputs
        if not self.validate_inputs(input_file, tagged_file, output_file):
            return False
        
        if validate_only:
            print("✓ All validations passed")
            return True
        
        # Reinitialize services if TTS engine override is provided
        if tts_engine:
            self._initialize_services(tts_engine_override=tts_engine)
        
        try:
            # Step 1: Text processing
            print("Step 1: Processing text with Gemini AI...")
            tagged_text = self.text_processor.process_text_file(
                input_file, tagged_file, force_tagging
            )
            if not tagged_text:
                return False
            
            if preview_only:
                # Show preview and exit
                preview_info = self.audio_synthesizer.preview_synthesis(tagged_text)
                print(f"Preview: {preview_info['total_segments']} segments, estimated {preview_info.get('estimated_total_duration_minutes', 0):.1f} minutes")
                return True
            
            # Step 2: Audio synthesis
            print("Step 2: Synthesizing audio...")
            self.audio_synthesizer.synthesize_text_to_audio(
                text=tagged_text,
                output_filename=output_file
            )
            
            print(f"✓ Audiobook created successfully: {output_file}")
            return True
            
        except KeyboardInterrupt:
            print("\n⚠ Processing interrupted by user")
            return False
        except Exception as e:
            print(f"Error during processing: {e}")
            return False


def main():
    """Main entry point for the CLI application."""
    parser = ArgumentParser()
    args = parser.parse_args()
    
    # Display help if no arguments provided
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    # Create processor instance
    processor = AudiobookProcessor()
    
    # Process the audiobook
    success = processor.process_audiobook(
        input_file=args.input_file,
        tagged_file=args.tagged_file,
        output_file=args.output_file,
        force_tagging=args.force_tagging,
        preview_only=args.preview,
        validate_only=args.validate_only,
        tts_engine=args.tts_engine
    )
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()