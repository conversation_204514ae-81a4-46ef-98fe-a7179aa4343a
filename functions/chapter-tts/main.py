"""
Function 2: Chapter TTS Processing with Pub/Sub Trigger
New function to handle TTS processing based on Terraform migration plan.
"""

import json
import logging
import os
import re
import sys
from datetime import datetime
from typing import Dict, Any, Optional

from google.cloud import storage
import firebase_admin
from firebase_admin import firestore
import base64

# Import for Cloud Functions framework
import functions_framework

# Suppress gRPC credential warnings
import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="grpc")

# Set explicit authentication environment
os.environ.setdefault('GOOGLE_APPLICATION_CREDENTIALS', '')
os.environ.setdefault('GRPC_VERBOSITY', 'ERROR')
os.environ.setdefault('GRPC_TRACE', '')

# Configure logging early
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import google-voice package from submodule (required - no fallback)
# Add google-voice submodule to Python path
import sys
import os
google_voice_path = os.path.join(os.path.dirname(__file__), 'google-voice')
if not os.path.exists(google_voice_path):
    raise RuntimeError(f"google-voice submodule not found at {google_voice_path}. Run build.sh to sync submodule.")

sys.path.insert(0, google_voice_path)
logger.info(f"Added google-voice path: {google_voice_path}")

# Import the TTS processor (this must succeed)
from api import process_text_to_audio
logger.info("Successfully imported google-voice TTS processor")



@functions_framework.cloud_event
def process_chapter_tts(cloud_event):
    """
    Cloud Function entry point for Pub/Sub message events (Gen2 CloudEvent format).
    Processes chapter TTS requests from the chapter processing topic.
    
    Args:
        cloud_event: CloudEvent object containing Pub/Sub message data
    """
    try:
        # Log the received cloud event for debugging
        logger.info(f"Received CloudEvent: {cloud_event}")
        logger.info(f"CloudEvent type: {getattr(cloud_event, 'type', 'unknown')}")
        logger.info(f"CloudEvent source: {getattr(cloud_event, 'source', 'unknown')}")
        
        # For Pub/Sub CloudEvents, the message data is in cloud_event.data
        # The data contains the Pub/Sub message in base64 format
        pubsub_message = cloud_event.data
        logger.info(f"Pub/Sub message data type: {type(pubsub_message)}")
        logger.info(f"Pub/Sub message keys: {list(pubsub_message.keys()) if isinstance(pubsub_message, dict) else 'Not a dict'}")
        
        # Extract the base64-encoded message data
        if 'message' in pubsub_message:
            # CloudEvent format: {"message": {"data": "base64_data", "attributes": {...}}}
            message_payload = pubsub_message['message']
            if 'data' in message_payload:
                encoded_data = message_payload['data']
                message_data = base64.b64decode(encoded_data).decode('utf-8')
            else:
                raise ValueError("No 'data' field in Pub/Sub message")
        elif 'data' in pubsub_message:
            # Alternative format: {"data": "base64_data"}
            encoded_data = pubsub_message['data']
            message_data = base64.b64decode(encoded_data).decode('utf-8')
        else:
            raise ValueError(f"Unexpected Pub/Sub message format: {pubsub_message}")
        
        # Debug: Log the raw message data to identify the invalid escape sequence
        logger.info(f"Raw message data (first 200 chars): {repr(message_data[:200])}")
        logger.info(f"Raw message data (last 200 chars): {repr(message_data[-200:])}")
        
        # Parse the JSON message with better error handling
        try:
            message = json.loads(message_data)
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"Error position: line {e.lineno}, column {e.colno}")
            # Find the problematic character around the error position
            if e.pos and e.pos < len(message_data):
                start = max(0, e.pos - 50)
                end = min(len(message_data), e.pos + 50)
                logger.error(f"Context around error: {repr(message_data[start:end])}")
            
            # Try to fix common JSON escape issues and retry parsing
            try:
                logger.info("Attempting to fix JSON escape issues...")
                # Fix common escape issues
                fixed_message_data = message_data
                # Fix unescaped backslashes (but be careful not to double-escape)
                fixed_message_data = re.sub(r'\\(?!["\\/bfnrtu])', r'\\\\', fixed_message_data)
                # Try parsing the fixed message
                message = json.loads(fixed_message_data)
                logger.info("Successfully parsed JSON after fixing escape issues")
            except json.JSONDecodeError as e2:
                logger.error(f"Failed to parse JSON even after escape fixing: {e2}")
                raise e  # Raise the original error
        logger.info(f"Processing TTS for chapter: {message.get('chapterId', 'unknown')}")
        logger.info(f"Message content length: {len(message.get('content', ''))}")
        
        # Validate required message fields
        required_fields = ['bookId', 'chapterId', 'content', 'outputBucket', 'outputPath']
        for field in required_fields:
            if field not in message:
                raise ValueError(f"Missing required field: {field}")
        
        # Log processing start time for timeout debugging
        start_time = datetime.utcnow()
        logger.info(f"Starting TTS processing at {start_time} for chapter: {message['chapterId']}")
        
        # Process the chapter TTS
        processor = ChapterTTSProcessor()
        result = processor.process_chapter_message(message)
        
        # Log completion time
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"Successfully processed TTS for chapter: {message['chapterId']} in {duration:.2f} seconds")
        return result
        
    except Exception as e:
        # Calculate duration for debugging
        end_time = datetime.utcnow() if 'start_time' in locals() else datetime.utcnow()
        duration = (end_time - start_time).total_seconds() if 'start_time' in locals() else 0
        
        logger.error(f"Error processing chapter TTS after {duration:.2f}s: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        
        # Extract chapter ID for graceful error handling
        chapter_id = 'unknown'
        book_id = 'unknown'
        try:
            chapter_id = message.get('chapterId', 'unknown') if 'message' in locals() else 'unknown'
            book_id = message.get('bookId', 'unknown') if 'message' in locals() else 'unknown'
        except:
            pass
            
        # Log the CloudEvent for debugging
        try:
            logger.error(f"Failed processing chapter: {chapter_id}")
            logger.error(f"Failed CloudEvent type: {type(cloud_event)}")
            logger.error(f"CloudEvent type: {getattr(cloud_event, 'type', 'unknown')}")
            logger.error(f"CloudEvent source: {getattr(cloud_event, 'source', 'unknown')}")
        except Exception as log_error:
            logger.error(f"Could not log CloudEvent data: {log_error}")
        
        # GRACEFUL ERROR HANDLING - Update Firestore status to prevent infinite retries
        try:
            if 'processor' in locals() and chapter_id != 'unknown' and book_id != 'unknown':
                logger.info(f"Updating chapter {chapter_id} status to 'error' to prevent retries")
                processor.update_chapter_status(
                    book_id, chapter_id, 'error',
                    correlation_id=message.get('correlationId', f"{book_id}-{chapter_id}") if 'message' in locals() else None,
                    error_code='FUNCTION_ERROR',
                    error_message=str(e)[:500],  # Truncate long error messages
                    error_at=datetime.utcnow()
                )
                logger.info(f"Successfully marked chapter {chapter_id} as failed - will not retry")
                # Return success to prevent Pub/Sub retries
                return {'status': 'failed_gracefully', 'chapter_id': chapter_id, 'error': str(e)[:200]}
            else:
                logger.warning("Could not update chapter status - insufficient context for graceful handling")
        except Exception as status_error:
            logger.error(f"Failed to update chapter status gracefully: {status_error}")
        
        # Only raise if we couldn't handle gracefully - this will trigger Pub/Sub retry
        logger.error("Could not handle error gracefully - will trigger retry")
        raise


class ChapterTTSProcessor:
    """Handles TTS processing for individual chapters"""
    
    def __init__(self):
        # Get environment configuration first
        self.environment = os.environ.get("ENVIRONMENT", "dev")
        self.project_id = os.environ.get("GCP_PROJECT") or os.environ.get("GOOGLE_CLOUD_PROJECT")
        
        # Force remove GOOGLE_APPLICATION_CREDENTIALS to use default authentication
        if 'GOOGLE_APPLICATION_CREDENTIALS' in os.environ:
            logger.info("DEBUG: Removing GOOGLE_APPLICATION_CREDENTIALS from environment")
            del os.environ['GOOGLE_APPLICATION_CREDENTIALS']
        
        # Initialize clients
        self.storage_client = storage.Client()
        
        # Cross-project Firestore configuration with Firebase Admin
        firestore_project = os.environ.get("FIRESTORE_PROJECT_ID", self.project_id)
        logger.info(f"TTS using Firestore project: {firestore_project}")
        
        # Initialize Firebase Admin SDK if not already initialized
        if not firebase_admin._apps:
            firebase_admin.initialize_app(options={
                'projectId': firestore_project,
                'databaseId': 'asia-southeast-1'
            })
        # Get Firestore client
        self.firestore_client = firestore.client()
        
        if not self.project_id:
            raise ValueError("Project ID not found in environment variables")
    
    def _get_secret(self, secret_name: str) -> str:
        """Retrieve secret from environment variables"""
        try:
            # Try direct environment variable first (recommended approach)
            if secret_name == "GEMINI_API_KEY":
                secret_value = os.environ.get("GEMINI_API_KEY")
                if secret_value:
                    logger.info("Successfully retrieved Gemini API key from environment variable")
                    return secret_value
            
            # Fallback to original secret name lookup for compatibility
            secret_value = os.environ.get(secret_name)
            if secret_value:
                return secret_value
                
            raise ValueError(f"Environment variable {secret_name} not found")
            
        except Exception as e:
            logger.error(f"Failed to retrieve secret {secret_name}: {e}")
            raise ValueError(f"Secret {secret_name} not found: {e}")
    
    def process_chapter_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single chapter TTS message"""
        book_id = message['bookId']
        chapter_id = message['chapterId']
        correlation_id = message.get('correlationId', f"{book_id}-{chapter_id}")
        
        try:
            # Update chapter status to processing
            self.update_chapter_status(
                book_id, chapter_id, 'processing',
                correlation_id=correlation_id,
                started_at=datetime.utcnow()
            )
            
            # Get chapter text content directly from message
            chapter_text = message['content']
            
            if not chapter_text or not chapter_text.strip():
                raise ValueError("Chapter text is empty or invalid")
            
            # Detailed text analysis for debugging
            text_length = len(chapter_text)
            text_bytes = len(chapter_text.encode('utf-8'))
            logger.info(f"Processing TTS for chapter {chapter_id}:")
            logger.info(f"  - Character count: {text_length}")
            logger.info(f"  - Byte length (UTF-8): {text_bytes}")
            logger.info(f"  - First 200 chars: {repr(chapter_text[:200])}")
            logger.info(f"  - Last 200 chars: {repr(chapter_text[-200:])}")
            
            if text_bytes > 5000:
                logger.warning(f"Chapter text exceeds Google Cloud TTS 5000-byte limit by {text_bytes - 5000} bytes")
            if text_length > 2000:
                logger.warning(f"Chapter text is quite long: {text_length} characters")
            
            # Generate audio using TTS
            audio_bytes = self.generate_audio(
                text=chapter_text,
                language=message.get('language', 'vi-VN'),
                voice=message.get('voice')
            )
            
            # Upload audio to GCS
            audio_gcs_uri = self.upload_audio_to_gcs(
                audio_bytes=audio_bytes,
                bucket_name=message['outputBucket'],
                object_path=message['outputPath']
            )
            
            # Calculate audio duration (rough estimate)
            duration_ms = self.estimate_audio_duration(audio_bytes)
            
            # Update chapter status to completed
            self.update_chapter_status(
                book_id, chapter_id, 'completed',
                correlation_id=correlation_id,
                audio_path=audio_gcs_uri,
                audio_format='WAV',
                duration_ms=duration_ms,
                size_bytes=len(audio_bytes),
                completed_at=datetime.utcnow()
            )
            
            logger.info(f"Successfully completed TTS for chapter {chapter_id}")
            
            return {
                'status': 'success',
                'book_id': book_id,
                'chapter_id': chapter_id,
                'audio_path': audio_gcs_uri,
                'duration_ms': duration_ms,
                'size_bytes': len(audio_bytes)
            }
            
        except Exception as e:
            logger.error(f"Error processing chapter {chapter_id}: {str(e)}")
            
            # Update chapter status to error
            try:
                self.update_chapter_status(
                    book_id, chapter_id, 'error',
                    correlation_id=correlation_id,
                    error_code='TTS_PROCESSING_ERROR',
                    error_message=str(e),
                    error_at=datetime.utcnow()
                )
            except Exception as db_error:
                logger.error(f"Failed to update error status: {db_error}")
            
            raise
    
    
    def generate_audio(self, text: str, language: str = 'vi-VN', voice: Optional[str] = None) -> bytes:
        """Generate audio from text using google-voice TTS processor"""
        # Always use google-voice processor - no fallback
        return self.generate_audio_with_processor(text, language, voice)
    
    def generate_audio_with_processor(self, text: str, language: str, voice: Optional[str]) -> bytes:
        """Generate audio using google-voice adapter pattern - always fails if google-voice fails"""
        
        text_length = len(text)
        text_bytes = len(text.encode('utf-8'))
        logger.info(f"Using google-voice adapter pattern:")
        logger.info(f"  - Input length: {text_length} characters, {text_bytes} bytes")
        
        if text_bytes > 4000:
            logger.warning(f"Large text input ({text_bytes} bytes) may cause issues with some TTS services")
        
        # Monkey patch sys.exit to prevent hard exits from submodule
        original_exit = sys.exit
        def safe_exit(code=0):
            logger.error(f"google-voice submodule called sys.exit({code}) - this is a fatal error")
            raise RuntimeError(f"google-voice submodule failed with exit code {code}")
        sys.exit = safe_exit
        
        # Get Gemini API key for advanced text processing
        gemini_api_key = None
        try:
            gemini_api_key = self._get_secret("GEMINI_API_KEY")
            logger.info("Gemini API key found, will use advanced text processing")
        except Exception as e:
            logger.error(f"GEMINI_API_KEY not found: {e}")
            raise RuntimeError(f"GEMINI_API_KEY is required for google-voice TTS: {e}")
        
        # Configure google-voice TTS with new adapter architecture
        config = {
            "gemini_api_key": gemini_api_key,
            "tts_engine": "google",  # Use Google TTS adapter
            "tts_config": {
                "language_code": language,
                "voice_name": voice,
                "speaking_rate": 0.99,
                "audio_encoding": "LINEAR16"
            },
            "timeout_seconds": 300,  # 5 minutes max for processing
            "max_retries": 2  # Limit retries to prevent long waits
        }
        
        try:
            logger.info("Starting TTS processing with google-voice adapter pattern")
            # Process text to audio
            audio_bytes = process_text_to_audio(
                text=text,
                config=config,
                return_bytes=True
            )
            
            logger.info(f"Generated audio using google-voice adapter: {len(audio_bytes)} bytes")
            return audio_bytes
            
        finally:
            sys.exit = original_exit  # Restore original sys.exit
    
    
    def upload_audio_to_gcs(self, audio_bytes: bytes, bucket_name: str, object_path: str) -> str:
        """Upload generated audio to GCS and return relative path"""
        try:
            # Remove leading slash if present for GCS blob path
            gcs_object_path = object_path.lstrip('/')
            
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(gcs_object_path)
            
            # Upload with appropriate content type
            blob.upload_from_string(
                audio_bytes,
                content_type='audio/wav'
            )
            
            logger.info(f"Audio uploaded to: gs://{bucket_name}/{gcs_object_path}")
            # Return relative path (not GCS URI)
            return object_path if object_path.startswith('/') else f'/{object_path}'
            
        except Exception as e:
            logger.error(f"Failed to upload audio to GCS: {e}")
            raise
    
    def estimate_audio_duration(self, audio_bytes: bytes) -> int:
        """Estimate audio duration in milliseconds"""
        try:
            # For 16-bit, 44.1kHz WAV files
            # Duration = bytes / (sample_rate * channels * bytes_per_sample)
            sample_rate = 44100
            channels = 1  # Mono
            bytes_per_sample = 2  # 16-bit
            
            # Account for WAV header (roughly 44 bytes)
            audio_data_size = max(0, len(audio_bytes) - 44)
            duration_seconds = audio_data_size / (sample_rate * channels * bytes_per_sample)
            duration_ms = int(duration_seconds * 1000)
            
            return duration_ms
            
        except Exception:
            # Return rough estimate based on text length
            # Assume ~150 words per minute, ~5 characters per word
            estimated_words = len(audio_bytes) // (sample_rate * 2) if audio_bytes else 0
            return max(1000, estimated_words * 400)  # Minimum 1 second
    
    def update_chapter_status(self, book_id: str, chapter_id: str, status: str, **kwargs) -> None:
        """Update chapter status in Firestore"""
        try:
            chapter_ref = (
                self.firestore_client
                .collection('books-tts')
                .document(book_id)
                .collection('chapters')
                .document(chapter_id)
            )
            
            update_data = {
                'status': status,
                'updatedAt': firestore.SERVER_TIMESTAMP
            }
            
            # Add optional fields
            if 'correlation_id' in kwargs:
                update_data['correlationId'] = kwargs['correlation_id']
            
            if 'started_at' in kwargs:
                update_data['startedAt'] = kwargs['started_at']
            
            if 'completed_at' in kwargs:
                update_data['completedAt'] = kwargs['completed_at']
            
            if 'audio_path' in kwargs:
                update_data['audioPath'] = kwargs['audio_path']
            
            if 'audio_format' in kwargs:
                update_data['audioFormat'] = kwargs['audio_format']
            
            if 'duration_ms' in kwargs:
                update_data['durationMs'] = kwargs['duration_ms']
            
            if 'size_bytes' in kwargs:
                update_data['audioSizeBytes'] = kwargs['size_bytes']
            
            if 'error_code' in kwargs or 'error_message' in kwargs:
                update_data['lastError'] = {
                    'code': kwargs.get('error_code', 'UNKNOWN_ERROR'),
                    'message': kwargs.get('error_message', 'Unknown error occurred'),
                    'at': kwargs.get('error_at', firestore.SERVER_TIMESTAMP)
                }
            
            if 'attempts' in kwargs:
                update_data['attempts'] = kwargs['attempts']
            else:
                # Increment attempts counter
                update_data['attempts'] = firestore.Increment(1)
            
            chapter_ref.set(update_data, merge=True)
            logger.info(f"Updated chapter {chapter_id} status to: {status}")
            
            # Also update book-level completion stats
            self.update_book_completion_stats(book_id)
            
        except Exception as e:
            logger.error(f"Failed to update chapter status in Firestore: {e}")
            # Don't raise here - status updates are important but non-critical
    
    def update_book_completion_stats(self, book_id: str) -> None:
        """Update book-level completion statistics"""
        try:
            # Query all chapters to get current stats
            chapters_ref = (
                self.firestore_client
                .collection('books-tts')
                .document(book_id)
                .collection('chapters')
            )
            
            chapters = chapters_ref.stream()
            completed_count = 0
            total_count = 0
            
            for chapter in chapters:
                total_count += 1
                if chapter.get('status') == 'completed':
                    completed_count += 1
            
            # Update book document
            book_ref = self.firestore_client.collection('books-tts').document(book_id)
            update_data = {
                'completedChapters': completed_count,
                'updatedAt': firestore.SERVER_TIMESTAMP
            }
            
            # Update overall status if all chapters are complete
            if completed_count == total_count and total_count > 0:
                update_data['status'] = 'completed'
            elif completed_count > 0:
                update_data['status'] = 'processing'
            
            book_ref.set(update_data, merge=True)
            
        except Exception as e:
            logger.error(f"Failed to update book completion stats: {e}")
            # Non-critical error