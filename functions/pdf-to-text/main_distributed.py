"""
PDF Outline Extraction Function - Distributed Architecture

This function only extracts the table of contents/outline from PDF books
and publishes individual chapter extraction messages to Pub/Sub.
The actual chapter text extraction is handled by the chapter-text-extraction function.
"""

import json
import logging
import tempfile
import os
import base64
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List

import functions_framework
from google.cloud import storage, pubsub_v1, secretmanager
from google.genai import Client as GenAIClient
from google.genai import types
import firebase_admin
from firebase_admin import firestore

# Import shared schema
import sys
sys.path.append('../shared')
from firestore_schema import (
    FirestoreSchema, 
    BookDocument, 
    ChapterDocument,
    BookStatus,
    ChapterStatus
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Firebase (safe for multiple initializations)
if not firebase_admin._apps:
    firebase_admin.initialize_app()

# Global clients - initialized lazily
_genai_client = None
_storage_client = None
_secret_client = None
_firestore_client = None
_firestore_schema = None
_publisher_client = None

def get_genai_client():
    """Get or create GenAI client with API key from Secret Manager"""
    global _genai_client, _secret_client
    
    if _genai_client is None:
        if _secret_client is None:
            _secret_client = secretmanager.SecretManagerServiceClient()
        
        # Get API key from Secret Manager
        project_id = os.environ.get('GOOGLE_CLOUD_PROJECT')
        secret_name = os.environ.get('GEMINI_SECRET_NAME', 'gemini-api-key')
        secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
        
        try:
            response = _secret_client.access_secret_version(request={"name": secret_path})
            api_key = response.payload.data.decode("UTF-8")
            
            _genai_client = GenAIClient(
                api_key=api_key,
                http_options={'api_version': 'v1beta'}
            )
            logger.info("GenAI client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize GenAI client: {str(e)}")
            raise
    
    return _genai_client

def get_storage_client():
    """Get or create Storage client"""
    global _storage_client
    if _storage_client is None:
        _storage_client = storage.Client()
    return _storage_client

def get_firestore_schema():
    """Get or create Firestore schema helper"""
    global _firestore_client, _firestore_schema
    
    if _firestore_schema is None:
        if _firestore_client is None:
            # Use asia-southeast1 database as specified in CLAUDE.md
            _firestore_client = firestore.Client(database="asia-southeast-1")
        
        _firestore_schema = FirestoreSchema(_firestore_client)
    
    return _firestore_schema

def get_publisher_client():
    """Get or create Pub/Sub publisher client"""
    global _publisher_client
    if _publisher_client is None:
        _publisher_client = pubsub_v1.PublisherClient()
    return _publisher_client

def extract_book_outline(pdf_base64: str, genai_client: GenAIClient, book_title: str = None) -> Dict[str, Any]:
    """
    Extract table of contents and chapter structure from PDF.
    
    Args:
        pdf_base64: Base64-encoded PDF content
        genai_client: Initialized GenAI client  
        book_title: Optional book title for context
        
    Returns:
        Dictionary with outline structure and chapter information
    """
    logger.info(f"DEBUG: Starting outline extraction for book: '{book_title}'")
    
    # Create outline extraction prompt
    prompt = f"""Please analyze this PDF book and extract a detailed table of contents with precise page numbers.

{'Book Title: ' + book_title if book_title else ''}

Instructions:
1. Extract ALL chapters, sections, and major headings
2. For EACH entry, provide:
   - Exact title as it appears in the book
   - Starting page number (critical for accurate extraction)
   - Ending page number (infer from next chapter start - 1)
3. Pay special attention to:
   - Chapter numbers (1, 2, 3... or I, II, III... or Chapter One, Two...)
   - Section divisions within chapters
   - Appendices, prefaces, introductions
4. Format the response as a JSON structure like this:

{{
  "title": "Book Title",
  "total_pages": 250,
  "orientation": "portrait or landscape",
  "chapters": [
    {{
      "title": "Chapter 1: Introduction",
      "start_page": 1,
      "end_page": 15,
      "order_index": 1
    }},
    {{
      "title": "Chapter 2: Getting Started",
      "start_page": 16,
      "end_page": 32,
      "order_index": 2
    }}
  ]
}}

CRITICAL: Be precise with page numbers. Each chapter must have accurate start_page and end_page values."""

    try:
        logger.info(f"DEBUG: Making GenAI API request for outline extraction")
        
        # Make sync API call to avoid event loop issues in Cloud Functions
        response = genai_client.models.generate_content(
            model='gemini-2.5-flash',
            contents=[
                types.Part.from_text(text=prompt),
                types.Part.from_bytes(
                    data=base64.b64decode(pdf_base64), 
                    mime_type="application/pdf"
                )
            ]
        )
        
        logger.info(f"DEBUG: GenAI API response received for outline extraction")
        
        if response and response.text:
            outline_text = response.text.strip()
            logger.info(f"DEBUG: Raw outline response: {outline_text[:200]}...")
            
            # Try to parse JSON response
            try:
                # Clean up response - remove markdown code blocks if present
                if '```json' in outline_text:
                    start = outline_text.find('```json') + 7
                    end = outline_text.find('```', start)
                    outline_text = outline_text[start:end].strip()
                elif '```' in outline_text:
                    start = outline_text.find('```') + 3
                    end = outline_text.find('```', start)
                    outline_text = outline_text[start:end].strip()
                
                outline_data = json.loads(outline_text)
                logger.info(f"DEBUG: Successfully parsed outline with {len(outline_data.get('chapters', []))} chapters")
                return outline_data
                
            except json.JSONDecodeError as e:
                logger.error(f"DEBUG: Failed to parse JSON response: {e}")
                logger.error(f"DEBUG: Response text: {outline_text}")
                raise ValueError(f"Invalid JSON response from outline extraction: {e}")
        else:
            raise ValueError("No outline content returned from API")
            
    except Exception as e:
        logger.error(f"DEBUG: GenAI API error for outline extraction: {str(e)}")
        raise

def load_pdf_from_gcs(bucket_name: str, pdf_path: str) -> str:
    """Load PDF file from GCS and return as base64"""
    logger.info(f"DEBUG: Loading PDF from GCS: gs://{bucket_name}/{pdf_path}")
    
    try:
        storage_client = get_storage_client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(pdf_path)
        
        if not blob.exists():
            raise FileNotFoundError(f"PDF file not found: gs://{bucket_name}/{pdf_path}")
        
        pdf_content = blob.download_as_bytes()
        pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
        
        logger.info(f"DEBUG: Successfully loaded PDF ({len(pdf_content)} bytes)")
        return pdf_base64
        
    except Exception as e:
        logger.error(f"DEBUG: Failed to load PDF from GCS: {str(e)}")
        raise

def publish_chapter_extraction_messages(
    chapters: List[Dict[str, Any]], 
    book_id: str, 
    pdf_path: str
) -> None:
    """
    Publish individual chapter extraction messages to Pub/Sub topic.
    
    Args:
        chapters: List of chapter information dictionaries
        book_id: Book identifier
        pdf_path: Path to PDF file in GCS
    """
    logger.info(f"DEBUG: Publishing {len(chapters)} chapter extraction messages")
    
    publisher = get_publisher_client()
    project_id = os.environ.get('GOOGLE_CLOUD_PROJECT')
    topic_name = os.environ.get('CHAPTER_TEXT_EXTRACTION_TOPIC_NAME', 'tts-dev-chapter-text-extraction')
    topic_path = publisher.topic_path(project_id, topic_name)
    
    try:
        for chapter in chapters:
            # Create message for chapter text extraction
            message_data = {
                'book_id': book_id,
                'chapter_id': f"chapter_{chapter['order_index']:02d}",
                'chapter_info': chapter,
                'pdf_path': pdf_path
            }
            
            # Serialize message
            message_json = json.dumps(message_data).encode('utf-8')
            
            # Publish message
            future = publisher.publish(topic_path, message_json)
            message_id = future.result()
            
            logger.info(f"DEBUG: Published chapter extraction message for '{chapter['title']}' (ID: {message_id})")
            
        logger.info(f"DEBUG: Successfully published all {len(chapters)} chapter extraction messages")
        
    except Exception as e:
        logger.error(f"DEBUG: Failed to publish chapter extraction messages: {str(e)}")
        raise

def process_pdf_outline(message_data: Dict[str, Any]):
    """
    Process PDF to extract outline and publish chapter extraction messages.
    
    Expected message format:
    {
        "bookId": "uuid",
        "title": "Book Title",
        "author": "Author Name",
        "sourcePdfPath": "books/book-uuid/book.pdf",
        "language": "vi-VN"
    }
    """
    book_id = message_data.get('bookId')
    title = message_data.get('title', 'Unknown Book')
    author = message_data.get('author')
    pdf_path = message_data.get('sourcePdfPath', '').lstrip('/')  # Remove leading slash
    language = message_data.get('language', 'vi-VN')
    bucket_name = os.environ.get('DATA_BUCKET_NAME')
    
    if not all([book_id, pdf_path, bucket_name]):
        raise ValueError(f"Missing required fields in message: {message_data}")
    
    logger.info(f"Processing PDF outline extraction: {book_id} - '{title}'")
    
    # Initialize clients
    genai_client = get_genai_client()
    firestore_schema = get_firestore_schema()
    
    try:
        # Create book document in Firestore
        book_doc = BookDocument(
            title=title,
            author=author,
            language=language,
            source_pdf_path=pdf_path,
            status=BookStatus.OUTLINE_EXTRACTING
        )
        
        firestore_schema.create_book(book_id, book_doc)
        logger.info(f"DEBUG: Created book document for {book_id}")
        
        # Load PDF from GCS
        pdf_base64 = load_pdf_from_gcs(bucket_name, pdf_path)
        
        # Extract outline
        outline_data = extract_book_outline(pdf_base64, genai_client, title)
        
        chapters = outline_data.get('chapters', [])
        if not chapters:
            raise ValueError("No chapters found in PDF outline")
        
        logger.info(f"DEBUG: Extracted {len(chapters)} chapters from outline")
        
        # Update book with chapter count and outline extracted status
        firestore_schema.update_book(book_id, {
            'chapterCount': len(chapters),
            'status': BookStatus.OUTLINE_EXTRACTED,
            'metadata': {
                'totalPages': outline_data.get('total_pages', 0),
                'orientation': outline_data.get('orientation', 'portrait')
            }
        })
        logger.info(f"DEBUG: Updated book with {len(chapters)} chapters")
        
        # Create chapter documents in Firestore
        for chapter_info in chapters:
            chapter_id = f"chapter_{chapter_info['order_index']:02d}"
            
            chapter_doc = ChapterDocument(
                book_id=book_id,
                title=chapter_info['title'],
                order_index=chapter_info['order_index'],
                content="",  # Will be populated by extraction function
                start_page=chapter_info.get('start_page'),
                end_page=chapter_info.get('end_page'),
                status=ChapterStatus.PENDING_EXTRACTION
            )
            
            firestore_schema.create_chapter(book_id, chapter_id, chapter_doc)
            
        logger.info(f"DEBUG: Created {len(chapters)} chapter documents")
        
        # Update book status to text extracting and publish chapter messages
        firestore_schema.update_book(book_id, {
            'status': BookStatus.TEXT_EXTRACTING
        })
        
        # Publish chapter extraction messages
        publish_chapter_extraction_messages(chapters, book_id, pdf_path)
        
        logger.info(f"Successfully completed outline extraction for book: {book_id}")
        
    except Exception as e:
        logger.error(f"Outline extraction failed for {book_id}: {str(e)}")
        
        # Update book with error status
        error_info = {
            'code': 'OUTLINE_EXTRACTION_ERROR',
            'message': str(e),
            'at': firestore.SERVER_TIMESTAMP
        }
        
        try:
            firestore_schema.update_book(book_id, {
                'status': BookStatus.ERROR,
                'lastError': error_info
            })
        except Exception as db_error:
            logger.error(f"Failed to update error status: {db_error}")
        
        raise

@functions_framework.cloud_event
def process_pdf_request(cloud_event):
    """
    Cloud Function entry point for Pub/Sub trigger.
    Processes PDF outline extraction requests.
    
    Args:
        cloud_event: Cloud Event containing Pub/Sub message
    """
    try:
        # Decode Pub/Sub message
        message_data = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode()
        )
        
        logger.info(f"Received PDF outline extraction request: {message_data}")
        
        # Run processing
        process_pdf_outline(message_data)
        
        logger.info("PDF outline extraction completed successfully")
        
    except Exception as e:
        logger.error(f"PDF outline extraction failed: {str(e)}")
        raise