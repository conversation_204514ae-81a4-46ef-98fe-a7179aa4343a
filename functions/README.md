# Cloud Functions

This directory contains the organized Cloud Functions for the book text-to-speech system, deployed via Terraform with event-driven architecture.

## Structure

```
functions/
├── pdf-to-text/           # Function 1: PDF Processing
│   ├── main.py           # Entry point (process_gcs_file)
│   ├── pdf_to_chapter.py # PDF processing logic  
│   ├── requirements.txt  # Python dependencies
│   └── Dockerfile        # Container configuration (optional)
├── chapter-tts/          # Function 2: TTS Processing
│   ├── main.py           # Entry point (process_chapter_tts)
│   ├── requirements.txt  # Dependencies
│   ├── Dockerfile        # Container with ffmpeg
│   ├── build.sh          # Syncs git submodule
│   └── google-voice/     # TTS processor (from git submodule)
└── shared/               # Common utilities
    ├── firestore_schema.py # Data models
    └── __init__.py
```

## Architecture

### Event-Driven Processing
1. **GCS Upload** → PDF Function → Chapter extraction → Pub/Sub messages
2. **Pub/Sub Messages** → TTS Function → Audio generation → GCS storage
3. **Progress Tracking** → Firestore documents updated by both functions

### Function Roles
- **pdf-to-text**: Converts PDFs to structured chapter data
- **chapter-tts**: Converts chapter text to audio files  
- **shared**: Common utilities and data models

## Deployment

### Via Terraform (Recommended)
```bash
cd terraform
./deploy-dev.sh              # Complete infrastructure
./update-functions.sh dev    # Functions only
```

### Manual Deployment
```bash
# Sync google-voice processor (required for chapter-tts)
cd functions/chapter-tts && ./build.sh

# Deploy functions individually  
gcloud functions deploy pdf-to-text --source=pdf-to-text --gen2
gcloud functions deploy chapter-tts --source=chapter-tts --gen2
```

## Development

### PDF Processing Function
```bash
cd functions/pdf-to-text
pip install -r requirements.txt    # Install dependencies
python main.py                     # Local testing
```

**Features:**
- GCS trigger on `books/[id]/book.pdf` uploads
- Google Generative AI chapter extraction
- Pub/Sub message publishing for each chapter
- Firestore progress tracking

### TTS Processing Function  
```bash
cd functions/chapter-tts
./build.sh                         # Sync google-voice processor submodule
pip install -r requirements.txt    # Install dependencies  
docker build -t chapter-tts .      # Test Docker build
```

**Features:**
- Pub/Sub trigger for chapter processing
- Docker container with system ffmpeg
- google-voice TTS processor from git submodule
- Audio file generation and GCS storage

### Shared Utilities
```bash
cd functions/shared
# Contains common data models and utilities
# Used by both functions for consistent data handling
```

## Key Technologies

### ✅ **Terraform Infrastructure**
- Infrastructure as Code deployment
- Multi-environment support (dev/staging/prod)
- Automatic API enablement and IAM setup

### ✅ **Docker Containerization** 
- chapter-tts function uses Docker for ffmpeg support
- Reliable system-level dependencies
- Consistent audio processing environment

### ✅ **Git Submodule Integration**
- google-voice TTS processor sourced from external repository
- `build.sh` script handles automatic syncing
- Clean separation of concerns

### ✅ **Event-Driven Architecture**
- GCS triggers for PDF uploads
- Pub/Sub messaging between functions
- Firestore state management
- Scalable parallel processing

### ✅ **Organized Function Structure**
- Clear separation between PDF and TTS processing
- Individual dependencies and configurations
- Shared utilities for common functionality

## Function Dependencies

### pdf-to-text
- `google-cloud-firestore`
- `google-cloud-storage`
- `google-cloud-pubsub`
- `google-generativeai`
- `pypdf2`

### chapter-tts
- `google-cloud-firestore`
- `google-cloud-storage`
- `google-cloud-texttospeech`
- `pydub` (with system ffmpeg)
- google-voice TTS processor (from git submodule)

## Monitoring & Debugging

```bash
# View function logs
gcloud functions logs read --region=asia-southeast1

# Check Pub/Sub metrics
gcloud pubsub topics list
gcloud pubsub subscriptions list  

# Monitor GCS processing
gsutil ls gs://fonos-dev/books/[book-id]/

# Firestore data (via console)
https://console.cloud.google.com/firestore
```

The functions are production-ready with comprehensive error handling, progress tracking, and scalable event-driven architecture.