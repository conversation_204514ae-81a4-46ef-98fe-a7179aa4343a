"""
Chapter Text Extraction Cloud Function

Processes individual chapters from PDF books to extract text content.
Triggered by Pub/Sub messages from the pdf-to-text function.
"""

import os
import json
import logging
import asyncio
import base64
import hashlib
from typing import Dict, Any, Optional

import functions_framework
from google.cloud import storage, secretmanager
from google.genai import Client as GenAIClient
from google.genai import types
import firebase_admin
from firebase_admin import firestore

# Import shared schema
import sys
sys.path.append('../shared')
from firestore_schema import (
    FirestoreSchema, 
    ChapterDocument, 
    ChapterStatus, 
    BookStatus,
    generate_content_checksum
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Firebase (safe for multiple initializations)
if not firebase_admin._apps:
    firebase_admin.initialize_app()

# Global clients - initialized lazily
_genai_client = None
_storage_client = None
_secret_client = None
_firestore_client = None
_firestore_schema = None

def get_genai_client():
    """Get or create GenAI client with API key from Secret Manager"""
    global _genai_client, _secret_client
    
    if _genai_client is None:
        if _secret_client is None:
            _secret_client = secretmanager.SecretManagerServiceClient()
        
        # Get API key from Secret Manager
        project_id = os.environ.get('GOOGLE_CLOUD_PROJECT')
        secret_name = os.environ.get('GEMINI_SECRET_NAME', 'gemini-api-key')
        secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
        
        try:
            response = _secret_client.access_secret_version(request={"name": secret_path})
            api_key = response.payload.data.decode("UTF-8")
            
            _genai_client = GenAIClient(
                api_key=api_key,
                http_options={'api_version': 'v1beta'}
            )
            logger.info("GenAI client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize GenAI client: {str(e)}")
            raise
    
    return _genai_client

def get_storage_client():
    """Get or create Storage client"""
    global _storage_client
    if _storage_client is None:
        _storage_client = storage.Client()
    return _storage_client

def get_firestore_schema():
    """Get or create Firestore schema helper"""
    global _firestore_client, _firestore_schema
    
    if _firestore_schema is None:
        if _firestore_client is None:
            # Use asia-southeast1 database as specified in CLAUDE.md
            _firestore_client = firestore.Client(database="asia-southeast-1")
        
        _firestore_schema = FirestoreSchema(_firestore_client)
    
    return _firestore_schema

async def extract_chapter_text(
    chapter_info: Dict[str, Any],
    pdf_base64: str,
    genai_client: GenAIClient
) -> str:
    """
    Extract text content for a single chapter from PDF using Gemini API.
    
    Args:
        chapter_info: Chapter metadata with start_page, end_page, title
        pdf_base64: Base64-encoded PDF content
        genai_client: Initialized GenAI client
        
    Returns:
        Extracted chapter text content
    """
    chapter_title = chapter_info.get('title', 'Unknown Chapter')
    start_page = chapter_info.get('start_page', 1)
    end_page = chapter_info.get('end_page', start_page)
    
    logger.info(f"DEBUG: Starting text extraction for chapter: '{chapter_title}' (pages {start_page}-{end_page})")
    
    # Create extraction prompt
    prompt = f"""Please extract all the text content from this chapter of a book.

Chapter Information:
- Title: {chapter_title}
- Pages: {start_page} to {end_page}

Instructions:
1. Extract ALL text content from the specified page range
2. Maintain paragraph structure and formatting where possible
3. Include headings, subheadings, and body text
4. Exclude page headers, footers, and page numbers
5. If there are tables or lists, convert them to readable text format
6. Return ONLY the extracted text content, no additional commentary

The text should be clean, well-formatted, and ready for text-to-speech processing."""

    try:
        logger.info(f"DEBUG: Making GenAI API request for chapter '{chapter_title}'")
        
        # Make async API call
        response = await genai_client.aio.models.generate_content(
            model='gemini-2.5-flash',
            contents=[
                types.Part.from_text(text=prompt),
                types.Part.from_bytes(
                    data=base64.b64decode(pdf_base64), 
                    mime_type="application/pdf"
                )
            ]
        )
        
        logger.info(f"DEBUG: GenAI API response received for chapter '{chapter_title}'")
        
        if response and response.text:
            extracted_text = response.text.strip()
            word_count = len(extracted_text.split())
            logger.info(f"DEBUG: Successfully extracted {word_count} words for chapter '{chapter_title}'")
            return extracted_text
        else:
            raise ValueError(f"No text content returned from API for chapter '{chapter_title}'")
            
    except Exception as e:
        logger.error(f"DEBUG: GenAI API error for chapter '{chapter_title}': {str(e)}")
        raise

def load_pdf_from_gcs(bucket_name: str, pdf_path: str) -> str:
    """Load PDF file from GCS and return as base64"""
    logger.info(f"DEBUG: Loading PDF from GCS: gs://{bucket_name}/{pdf_path}")
    
    try:
        storage_client = get_storage_client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(pdf_path)
        
        if not blob.exists():
            raise FileNotFoundError(f"PDF file not found: gs://{bucket_name}/{pdf_path}")
        
        pdf_content = blob.download_as_bytes()
        pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
        
        logger.info(f"DEBUG: Successfully loaded PDF ({len(pdf_content)} bytes)")
        return pdf_base64
        
    except Exception as e:
        logger.error(f"DEBUG: Failed to load PDF from GCS: {str(e)}")
        raise

async def process_chapter_extraction_async(message_data: Dict[str, Any]):
    """
    Process a single chapter text extraction message.
    
    Expected message format:
    {
        "book_id": "uuid",
        "chapter_id": "chapter_01",
        "chapter_info": {
            "title": "Chapter 1: Introduction",
            "start_page": 1,
            "end_page": 15,
            "order_index": 1
        },
        "pdf_path": "books/book-uuid/book.pdf"
    }
    """
    book_id = message_data.get('book_id')
    chapter_id = message_data.get('chapter_id')
    chapter_info = message_data.get('chapter_info', {})
    pdf_path = message_data.get('pdf_path')
    bucket_name = os.environ.get('DATA_BUCKET_NAME')
    
    if not all([book_id, chapter_id, pdf_path, bucket_name]):
        raise ValueError(f"Missing required fields in message: {message_data}")
    
    logger.info(f"Processing chapter extraction: {book_id}/{chapter_id}")
    
    # Initialize clients
    genai_client = get_genai_client()
    firestore_schema = get_firestore_schema()
    
    try:
        # Update chapter status to extracting
        firestore_schema.update_chapter(book_id, chapter_id, {
            'status': ChapterStatus.EXTRACTING,
            'startedAt': firestore.SERVER_TIMESTAMP
        })
        logger.info(f"DEBUG: Updated chapter {chapter_id} status to extracting")
        
        # Load PDF from GCS
        pdf_base64 = load_pdf_from_gcs(bucket_name, pdf_path)
        
        # Extract text content
        extracted_text = await extract_chapter_text(chapter_info, pdf_base64, genai_client)
        
        # Generate content checksum
        content_checksum = generate_content_checksum(extracted_text)
        word_count = len(extracted_text.split())
        
        # Update chapter with extracted content
        chapter_updates = {
            'content': extracted_text,
            'contentChecksum': content_checksum,
            'wordCount': word_count,
            'status': ChapterStatus.EXTRACTED,
            'completedAt': firestore.SERVER_TIMESTAMP
        }
        
        firestore_schema.update_chapter(book_id, chapter_id, chapter_updates)
        logger.info(f"DEBUG: Updated chapter {chapter_id} with extracted text ({word_count} words)")
        
        # Atomically increment extracted chapters counter
        book_data = firestore_schema.increment_extracted_chapters(book_id)
        logger.info(f"DEBUG: Incremented extracted chapters counter. Book status: {book_data.get('status')}")
        
        # Check if all chapters are extracted and trigger TTS processing
        if book_data.get('status') == BookStatus.TEXT_EXTRACTED:
            logger.info(f"DEBUG: All chapters extracted for book {book_id}. Triggering TTS processing.")
            # TODO: Publish message to chapter TTS topic for each chapter
            # This will be implemented when we update the TTS function
            
        logger.info(f"Successfully completed chapter extraction: {book_id}/{chapter_id}")
        
    except Exception as e:
        logger.error(f"Chapter extraction failed for {book_id}/{chapter_id}: {str(e)}")
        
        # Update chapter with error status
        error_info = {
            'code': 'EXTRACTION_ERROR',
            'message': str(e),
            'at': firestore.SERVER_TIMESTAMP
        }
        
        firestore_schema.update_chapter(book_id, chapter_id, {
            'status': ChapterStatus.ERROR,
            'lastError': error_info,
            'attempts': firestore.Increment(1)
        })
        
        # Update book status if needed
        firestore_schema.update_book(book_id, {
            'status': BookStatus.ERROR,
            'lastError': error_info
        })
        
        raise

@functions_framework.cloud_event
def process_chapter_text_extraction(cloud_event):
    """
    Cloud Function entry point for Pub/Sub trigger.
    
    Args:
        cloud_event: Cloud Event containing Pub/Sub message
    """
    try:
        # Decode Pub/Sub message
        message_data = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode()
        )
        
        logger.info(f"Received chapter extraction request: {message_data}")
        
        # Run async processing
        asyncio.run(process_chapter_extraction_async(message_data))
        
        logger.info("Chapter text extraction completed successfully")
        
    except Exception as e:
        logger.error(f"Chapter text extraction failed: {str(e)}")
        raise