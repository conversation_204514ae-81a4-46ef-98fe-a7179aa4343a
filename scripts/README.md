# PDF Processing Scripts

This directory contains scripts for manually triggering PDF processing in the book text-to-speech system.

## Overview

The new approach avoids job stealing issues with other GCS-triggered functions by using a manual trigger system:

1. Upload PDF files to GCS without triggering automatic processing
2. Use the trigger script to create Firestore records and publish Pub/Sub messages
3. The PDF processing function responds to Pub/Sub messages instead of GCS events

## Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Set up authentication:
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"
   ```

3. Set environment variables:
   ```bash
   export GCP_PROJECT="your-project-id"
   export FIRESTORE_PROJECT_ID="your-project-id"  # Usually same as GCP_PROJECT
   export PDF_PROCESSING_TOPIC="tts-dev-pdf-process"  # Or appropriate topic name
   ```

## Usage

### Basic Usage

```bash
python trigger_pdf_processing.py \
  --book-id "book-123" \
  --title "My Awesome Book" \
  --author "<PERSON>"
```

### With Custom PDF Path

```bash
python trigger_pdf_processing.py \
  --book-id "book-123" \
  --title "My Awesome Book" \
  --author "John Doe" \
  --pdf-path "books/book-123/book.pdf"
```

### With Different Language

```bash
python trigger_pdf_processing.py \
  --book-id "book-123" \
  --title "My English Book" \
  --author "Jane Smith" \
  --language "en-US"
```

### Override Environment Variables

```bash
python trigger_pdf_processing.py \
  --book-id "book-123" \
  --title "My Book" \
  --project-id "my-project" \
  --topic-name "my-pdf-topic"
```

## Workflow

1. **Upload PDF**: First, upload your PDF file to GCS:
   ```bash
   gsutil cp my-book.pdf gs://your-bucket/books/book-123/book.pdf
   ```

2. **Trigger Processing**: Run the trigger script:
   ```bash
   python trigger_pdf_processing.py --book-id "book-123" --title "My Book"
   ```

3. **Monitor Progress**: Check the Cloud Function logs to monitor processing:
   ```bash
   gcloud functions logs read --region=asia-southeast1 --limit=50
   ```

## Script Arguments

- `--book-id` (required): Unique identifier for the book
- `--title` (required): Book title
- `--author` (optional): Book author
- `--language` (optional): Book language (default: vi-VN)
- `--pdf-path` (optional): Custom PDF path in GCS (default: books/{book-id}/book.pdf)
- `--project-id` (optional): Override GCP_PROJECT environment variable
- `--firestore-project-id` (optional): Override FIRESTORE_PROJECT_ID environment variable
- `--topic-name` (optional): Override PDF_PROCESSING_TOPIC environment variable

## Environment Variables

- `GOOGLE_APPLICATION_CREDENTIALS`: Path to service account key file
- `GCP_PROJECT`: Google Cloud Project ID
- `FIRESTORE_PROJECT_ID`: Firestore project ID (usually same as GCP_PROJECT)
- `PDF_PROCESSING_TOPIC`: PDF processing Pub/Sub topic name
  - Dev: `tts-dev-pdf-process`
  - Staging: `tts-staging-pdf-process`
  - Prod: `tts-pdf-process`

## Output

The script outputs a JSON result with the operation status:

```json
{
  "status": "success",
  "book_id": "book-123",
  "message_id": "**********",
  "firestore_created": true,
  "message_published": true
}
```

## Error Handling

If an error occurs, the script will output an error result:

```json
{
  "status": "error",
  "book_id": "book-123",
  "error": "Error message details"
}
```

## Integration with Admin System

This script serves as a prototype for the admin system functionality. The admin system should implement similar logic to:

1. Create Firestore records with book metadata
2. Publish messages to the PDF processing topic
3. Handle user uploads and trigger processing

## Troubleshooting

### Authentication Issues
- Ensure `GOOGLE_APPLICATION_CREDENTIALS` points to a valid service account key
- Verify the service account has necessary permissions for Firestore and Pub/Sub

### Topic Not Found
- Check that the PDF processing topic exists in your project
- Verify the topic name matches your environment (dev/staging/prod)

### Permission Denied
- Ensure the service account has the following roles:
  - Cloud Datastore User (for Firestore)
  - Pub/Sub Publisher
  - Storage Object Viewer (if accessing GCS)

### Function Not Triggering
- Verify the Cloud Function is deployed with the correct Pub/Sub trigger
- Check that the function entry point is set to `process_pdf_request`
- Monitor Cloud Function logs for any errors
