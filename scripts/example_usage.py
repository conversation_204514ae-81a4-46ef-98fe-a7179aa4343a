#!/usr/bin/env python3
"""
Example usage of the PDF processing trigger script

This script demonstrates how to use the trigger_pdf_processing module
programmatically instead of via command line.
"""

import os
import json
from trigger_pdf_processing import PDFProcessingTrigger

def main():
    """Example of programmatic usage"""
    
    # Configuration (normally from environment variables)
    project_id = os.environ.get('GCP_PROJECT', 'fonos-audio')
    firestore_project_id = os.environ.get('FIRESTORE_PROJECT_ID', project_id)
    topic_name = os.environ.get('PDF_PROCESSING_TOPIC', 'tts-dev-pdf-process')
    
    # Initialize the trigger
    trigger = PDFProcessingTrigger(project_id, firestore_project_id, topic_name)
    
    # Example 1: Basic book processing
    print("Example 1: Basic book processing")
    result1 = trigger.trigger_processing(
        book_id="example-book-001",
        title="Introduction to Machine Learning",
        author="<PERSON><PERSON> <PERSON>",
        language="en-US"
    )
    print(json.dumps(result1, indent=2))
    print()
    
    # Example 2: Vietnamese book with custom path
    print("Example 2: Vietnamese book with custom path")
    result2 = trigger.trigger_processing(
        book_id="vn-book-002",
        title="Lập trình Python cơ bản",
        author="Nguyễn Văn A",
        language="vi-VN",
        pdf_path="/books/vn-book-002/book.pdf"
    )
    print(json.dumps(result2, indent=2))
    print()
    
    # Example 3: Book without author
    print("Example 3: Book without author")
    result3 = trigger.trigger_processing(
        book_id="anonymous-book-003",
        title="Open Source Programming Guide"
    )
    print(json.dumps(result3, indent=2))

if __name__ == '__main__':
    main()
