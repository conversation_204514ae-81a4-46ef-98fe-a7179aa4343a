#!/usr/bin/env python3
"""
Simple script to check Firestore documents in the correct project
"""

import os
import sys
from google.cloud import firestore

def check_firestore_document(project_id, collection, document_id):
    """Check if a document exists in Firestore"""
    try:
        # Initialize Firestore client
        client = firestore.Client(project=project_id)
        
        # Get the document
        doc_ref = client.collection(collection).document(document_id)
        doc = doc_ref.get()
        
        if doc.exists:
            print(f"✅ Document found in project '{project_id}':")
            print(f"   Collection: {collection}")
            print(f"   Document ID: {document_id}")
            print(f"   Data: {doc.to_dict()}")
            return True
        else:
            print(f"❌ Document NOT found in project '{project_id}':")
            print(f"   Collection: {collection}")
            print(f"   Document ID: {document_id}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Firestore: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 3 or len(sys.argv) > 4:
        print("Usage: python check_firestore.py <project_id> <document_id> [collection]")
        print("Default collection: books-tts")
        sys.exit(1)

    project_id = sys.argv[1]
    document_id = sys.argv[2]
    collection = sys.argv[3] if len(sys.argv) == 4 else "books-tts"

    check_firestore_document(project_id, collection, document_id)
