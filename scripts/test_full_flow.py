#!/usr/bin/env python3
"""
Full Flow Test Script

This script tests the complete book text-to-speech pipeline by:
1. Uploading a PDF file to GCS
2. Creating a Firestore record with book metadata
3. Publishing a message to trigger PDF processing
4. Verifying the Firestore document was created correctly
5. Monitoring the processing logs

Usage:
    python test_full_flow.py --book-id test-book --title "Test Book" --pdf-file ../book.pdf

Environment Variables Required:
    - GOOGLE_APPLICATION_CREDENTIALS: Path to service account key file
    - GCP_PROJECT: Google Cloud Project ID for Pub/Sub and GCS
    - FIRESTORE_PROJECT_ID: Firestore project ID (usually different from GCP_PROJECT)
    - PDF_PROCESSING_TOPIC: PDF processing Pub/Sub topic name
    - DATA_BUCKET_NAME: GCS bucket name for storing PDFs
"""

import argparse
import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from google.cloud import storage, firestore, pubsub_v1

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FullFlowTester:
    """Handles complete end-to-end testing of the book TTS pipeline"""
    
    def __init__(self, project_id: str, firestore_project_id: str, 
                 topic_name: str, bucket_name: str):
        """Initialize the tester with GCP configuration
        
        Args:
            project_id: GCP project ID for Pub/Sub and GCS
            firestore_project_id: Firestore project ID
            topic_name: PDF processing Pub/Sub topic name
            bucket_name: GCS bucket name for storing PDFs
        """
        self.project_id = project_id
        self.firestore_project_id = firestore_project_id
        self.topic_name = topic_name
        self.bucket_name = bucket_name
        
        # Initialize clients
        try:
            self.storage_client = storage.Client(project=project_id)
            self.bucket = self.storage_client.bucket(bucket_name)
            self.firestore_client = firestore.Client(project=firestore_project_id)
            self.publisher = pubsub_v1.PublisherClient()
            self.topic_path = self.publisher.topic_path(project_id, topic_name)
            logger.info(f"Initialized clients:")
            logger.info(f"  GCP Project: {project_id}")
            logger.info(f"  Firestore Project: {firestore_project_id}")
            logger.info(f"  Bucket: {bucket_name}")
            logger.info(f"  Topic: {topic_name}")
        except Exception as e:
            logger.error(f"Failed to initialize GCP clients: {e}")
            raise
    
    def upload_pdf_to_gcs(self, local_pdf_path: str, book_id: str) -> str:
        """Upload PDF file to GCS
        
        Args:
            local_pdf_path: Path to local PDF file
            book_id: Unique book identifier
            
        Returns:
            GCS path of uploaded file
        """
        try:
            if not os.path.exists(local_pdf_path):
                raise FileNotFoundError(f"PDF file not found: {local_pdf_path}")
            
            # Define GCS path
            gcs_path = f"books/{book_id}/book.pdf"
            blob = self.bucket.blob(gcs_path)
            
            # Upload file
            logger.info(f"Uploading {local_pdf_path} to gs://{self.bucket_name}/{gcs_path}")
            blob.upload_from_filename(local_pdf_path)
            
            # Get file size for logging
            file_size = os.path.getsize(local_pdf_path)
            logger.info(f"✅ Successfully uploaded PDF ({file_size:,} bytes)")
            
            return f"/{gcs_path}"
            
        except Exception as e:
            logger.error(f"Failed to upload PDF to GCS: {e}")
            raise
    
    def create_firestore_record(self, book_id: str, title: str, author: str = None, 
                               language: str = "en-US", pdf_path: str = None) -> Dict[str, Any]:
        """Create a Firestore record for the book
        
        Args:
            book_id: Unique book identifier
            title: Book title
            author: Book author (optional)
            language: Book language (default: en-US)
            pdf_path: Path to PDF file in GCS
            
        Returns:
            Dictionary with the created document data
        """
        try:
            book_ref = self.firestore_client.collection('books-tts').document(book_id)
            
            # Prepare book data
            book_data = {
                'title': title,
                'language': language,
                'sourcePdfPath': pdf_path or f"/books/{book_id}/book.pdf",
                'status': 'new',
                'chapterCount': 0,
                'completedChapters': 0,
                'createdAt': firestore.SERVER_TIMESTAMP,
                'updatedAt': firestore.SERVER_TIMESTAMP
            }
            
            if author:
                book_data['author'] = author
            
            # Create/update the document
            book_ref.set(book_data, merge=True)
            logger.info(f"Created Firestore record for book: {book_id}")
            
            # Verify the document was created by reading it back
            return self.verify_firestore_record(book_id)
            
        except Exception as e:
            logger.error(f"Failed to create Firestore record: {e}")
            raise
    
    def verify_firestore_record(self, book_id: str) -> Dict[str, Any]:
        """Verify that a Firestore record exists and return its data
        
        Args:
            book_id: Unique book identifier
            
        Returns:
            Dictionary with the document data
        """
        try:
            book_ref = self.firestore_client.collection('books-tts').document(book_id)
            doc = book_ref.get()
            
            if doc.exists:
                doc_data = doc.to_dict()
                logger.info(f"✅ Verified Firestore record exists for book: {book_id}")
                logger.info(f"   Project: {self.firestore_project_id}")
                logger.info(f"   Collection: books-tts")
                logger.info(f"   Document data: {doc_data}")
                return doc_data
            else:
                raise Exception(f"Document {book_id} was not found in Firestore after creation")
                
        except Exception as e:
            logger.error(f"Failed to verify Firestore record: {e}")
            raise
    
    def publish_pdf_processing_message(self, book_id: str, title: str, author: str = None,
                                     language: str = "en-US", pdf_path: str = None) -> str:
        """Publish a message to trigger PDF processing
        
        Args:
            book_id: Unique book identifier
            title: Book title
            author: Book author (optional)
            language: Book language (default: en-US)
            pdf_path: Path to PDF file in GCS
            
        Returns:
            Message ID from Pub/Sub
        """
        try:
            # Prepare message data
            message_data = {
                'bookId': book_id,
                'title': title,
                'language': language,
                'sourcePdfPath': pdf_path or f"/books/{book_id}/book.pdf",
                'timestamp': datetime.utcnow().isoformat()
            }
            
            if author:
                message_data['author'] = author
            
            # Convert to JSON and encode
            message_json = json.dumps(message_data, ensure_ascii=False)
            message_bytes = message_json.encode('utf-8')
            
            # Publish message
            future = self.publisher.publish(self.topic_path, message_bytes)
            message_id = future.result(timeout=30)
            
            logger.info(f"✅ Published PDF processing message: {message_id}")
            logger.info(f"   Message data: {message_data}")
            
            return message_id
            
        except Exception as e:
            logger.error(f"Failed to publish message: {e}")
            raise
    
    def run_full_test(self, book_id: str, title: str, local_pdf_path: str,
                     author: str = None, language: str = "en-US") -> Dict[str, Any]:
        """Run the complete end-to-end test
        
        Args:
            book_id: Unique book identifier
            title: Book title
            local_pdf_path: Path to local PDF file
            author: Book author (optional)
            language: Book language (default: en-US)
            
        Returns:
            Dictionary with test results
        """
        try:
            logger.info(f"🚀 Starting full flow test for book: {book_id}")
            logger.info(f"   Title: {title}")
            logger.info(f"   PDF: {local_pdf_path}")
            logger.info(f"   Author: {author}")
            logger.info(f"   Language: {language}")
            
            # Step 1: Upload PDF to GCS
            logger.info("📤 Step 1: Uploading PDF to GCS...")
            pdf_path = self.upload_pdf_to_gcs(local_pdf_path, book_id)
            
            # Step 2: Create Firestore record and verify it
            logger.info("📝 Step 2: Creating Firestore record...")
            firestore_data = self.create_firestore_record(book_id, title, author, language, pdf_path)
            
            # Step 3: Publish processing message
            logger.info("📨 Step 3: Publishing processing message...")
            message_id = self.publish_pdf_processing_message(book_id, title, author, language, pdf_path)
            
            result = {
                'status': 'success',
                'book_id': book_id,
                'pdf_uploaded': True,
                'pdf_path': pdf_path,
                'firestore_created': True,
                'firestore_verified': True,
                'firestore_project': self.firestore_project_id,
                'firestore_collection': 'books-tts',
                'firestore_data': firestore_data,
                'message_published': True,
                'message_id': message_id,
                'test_completed_at': datetime.utcnow().isoformat()
            }
            
            logger.info(f"🎉 Successfully completed full flow test for book: {book_id}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Full flow test failed: {e}")
            return {
                'status': 'error',
                'book_id': book_id,
                'firestore_project': self.firestore_project_id,
                'error': str(e),
                'test_failed_at': datetime.utcnow().isoformat()
            }


def main():
    """Main function to handle command line arguments and execute testing"""
    parser = argparse.ArgumentParser(description='Test complete book TTS pipeline')
    parser.add_argument('--book-id', required=True, help='Unique book identifier')
    parser.add_argument('--title', required=True, help='Book title')
    parser.add_argument('--pdf-file', required=True, help='Path to local PDF file')
    parser.add_argument('--author', help='Book author (optional)')
    parser.add_argument('--language', default='en-US', help='Book language (default: en-US)')
    
    # Override environment variables
    parser.add_argument('--project-id', help='GCP project ID (overrides environment variable)')
    parser.add_argument('--firestore-project-id', help='Firestore project ID (overrides environment variable)')
    parser.add_argument('--topic-name', help='PDF processing topic name (overrides environment variable)')
    parser.add_argument('--bucket-name', help='GCS bucket name (overrides environment variable)')
    
    args = parser.parse_args()
    
    # Get configuration from environment variables or command line
    project_id = args.project_id or os.environ.get('GCP_PROJECT')
    firestore_project_id = args.firestore_project_id or os.environ.get('FIRESTORE_PROJECT_ID') or project_id
    topic_name = args.topic_name or os.environ.get('PDF_PROCESSING_TOPIC')
    bucket_name = args.bucket_name or os.environ.get('DATA_BUCKET_NAME')
    
    # Validate required configuration
    if not project_id:
        logger.error("GCP_PROJECT environment variable or --project-id argument required")
        sys.exit(1)
    
    if not topic_name:
        logger.error("PDF_PROCESSING_TOPIC environment variable or --topic-name argument required")
        sys.exit(1)
    
    if not bucket_name:
        logger.error("DATA_BUCKET_NAME environment variable or --bucket-name argument required")
        sys.exit(1)
    
    try:
        # Initialize tester
        tester = FullFlowTester(project_id, firestore_project_id, topic_name, bucket_name)
        
        # Execute full test
        result = tester.run_full_test(
            book_id=args.book_id,
            title=args.title,
            local_pdf_path=args.pdf_file,
            author=args.author,
            language=args.language
        )
        
        # Print result (convert timestamps to strings for JSON serialization)
        result_for_print = result.copy()
        if 'firestore_data' in result_for_print:
            firestore_data = result_for_print['firestore_data'].copy()
            for key, value in firestore_data.items():
                if hasattr(value, 'isoformat'):  # Handle datetime objects
                    firestore_data[key] = value.isoformat()
            result_for_print['firestore_data'] = firestore_data
        
        print("\n" + "="*60)
        print("FULL FLOW TEST RESULTS")
        print("="*60)
        print(json.dumps(result_for_print, indent=2))
        
        if result['status'] == 'success':
            logger.info("✅ Full flow test completed successfully!")
            print("\n🎉 Next steps:")
            print("   1. Monitor Cloud Function logs for PDF processing")
            print("   2. Check Firestore for chapter documents")
            print("   3. Verify audio files are generated in GCS")
            sys.exit(0)
        else:
            logger.error("❌ Full flow test failed")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Critical error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
