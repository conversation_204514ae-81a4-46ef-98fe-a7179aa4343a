#!/usr/bin/env python3
"""
Manual PDF Processing Trigger Script

This script allows manual triggering of PDF processing by:
1. Creating a Firestore record with book metadata
2. Publishing a message to the PDF processing Pub/Sub topic

Usage:
    python trigger_pdf_processing.py --book-id book-123 --title "My Book" --author "Author Name" --pdf-path "books/book-123/book.pdf"

Environment Variables Required:
    - GOOGLE_APPLICATION_CREDENTIALS: Path to service account key file
    - GCP_PROJECT: Google Cloud Project ID
    - FIRESTORE_PROJECT_ID: Firestore project ID (usually same as GCP_PROJECT)
    - PDF_PROCESSING_TOPIC: PDF processing Pub/Sub topic name
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional

from google.cloud import pubsub_v1
import firebase_admin
from firebase_admin import firestore

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PDFProcessingTrigger:
    """Handles manual triggering of PDF processing workflow"""
    
    def __init__(self, project_id: str, firestore_project_id: str, topic_name: str):
        """Initialize the trigger with GCP configuration
        
        Args:
            project_id: GCP project ID
            firestore_project_id: Firestore project ID
            topic_name: PDF processing Pub/Sub topic name
        """
        self.project_id = project_id
        self.firestore_project_id = firestore_project_id
        self.topic_name = topic_name

        print(f"Project ID: {project_id}")
        print(f"Firestore Project ID: {firestore_project_id}")
        print(f"Topic Name: {topic_name}")
        
        # Initialize clients
        try:
            # Initialize Firebase Admin SDK if not already initialized
            if not firebase_admin._apps:
                firebase_admin.initialize_app(options={
                    'projectId': firestore_project_id,
                    'databaseId': 'asia-southeast-1'
                })
            self.firestore_client = firestore.client()
            self.publisher = pubsub_v1.PublisherClient()
            self.topic_path = self.publisher.topic_path(project_id, topic_name)
            logger.info(f"Initialized clients for project: {project_id}")
        except Exception as e:
            logger.error(f"Failed to initialize GCP clients: {e}")
            raise
    
    def create_firestore_record(self, book_id: str, title: str, author: str = None,
                               language: str = "vi-VN", pdf_path: str = None) -> Dict[str, Any]:
        """Create a Firestore record for the book

        Args:
            book_id: Unique book identifier
            title: Book title
            author: Book author (optional)
            language: Book language (default: vi-VN)
            pdf_path: Path to PDF file in GCS

        Returns:
            Dictionary with the created document data
        """
        try:
            book_ref = self.firestore_client.collection('books-tts').document(book_id)

            # Prepare book data
            book_data = {
                'title': title,
                'language': language,
                'sourcePdfPath': pdf_path or f"/books/{book_id}/book.pdf",
                'status': 'new',
                'chapterCount': 0,
                'completedChapters': 0,
                'createdAt': firestore.SERVER_TIMESTAMP,
                'updatedAt': firestore.SERVER_TIMESTAMP
            }

            if author:
                book_data['author'] = author

            # Create/update the document
            book_ref.set(book_data, merge=True)
            logger.info(f"Created Firestore record for book: {book_id}")

            # Verify the document was created by reading it back
            return self.verify_firestore_record(book_id)

        except Exception as e:
            logger.error(f"Failed to create Firestore record: {e}")
            raise

    def verify_firestore_record(self, book_id: str) -> Dict[str, Any]:
        """Verify that a Firestore record exists and return its data

        Args:
            book_id: Unique book identifier

        Returns:
            Dictionary with the document data
        """
        try:
            book_ref = self.firestore_client.collection('books-tts').document(book_id)
            doc = book_ref.get()

            if doc.exists:
                doc_data = doc.to_dict()
                logger.info(f"✅ Verified Firestore record exists for book: {book_id}")
                logger.info(f"   Project: {self.firestore_project_id}")
                logger.info(f"   Document data: {doc_data}")
                return doc_data
            else:
                raise Exception(f"Document {book_id} was not found in Firestore after creation")

        except Exception as e:
            logger.error(f"Failed to verify Firestore record: {e}")
            raise
    
    def publish_pdf_processing_message(self, book_id: str, title: str, author: str = None,
                                     language: str = "vi-VN", pdf_path: str = None) -> str:
        """Publish a message to trigger PDF processing
        
        Args:
            book_id: Unique book identifier
            title: Book title
            author: Book author (optional)
            language: Book language (default: vi-VN)
            pdf_path: Path to PDF file in GCS
            
        Returns:
            Message ID from Pub/Sub
        """
        try:
            # Prepare message data
            message_data = {
                'bookId': book_id,
                'title': title,
                'language': language,
                'sourcePdfPath': pdf_path or f"/books/{book_id}/book.pdf",
                'timestamp': datetime.utcnow().isoformat()
            }
            
            if author:
                message_data['author'] = author
            
            # Convert to JSON and encode
            message_json = json.dumps(message_data, ensure_ascii=False)
            message_bytes = message_json.encode('utf-8')
            
            # Publish message
            future = self.publisher.publish(self.topic_path, message_bytes)
            message_id = future.result(timeout=30)
            
            logger.info(f"Published PDF processing message: {message_id}")
            logger.info(f"Message data: {message_data}")
            
            return message_id
            
        except Exception as e:
            logger.error(f"Failed to publish message: {e}")
            raise
    
    def trigger_processing(self, book_id: str, title: str, author: str = None,
                          language: str = "vi-VN", pdf_path: str = None) -> Dict[str, Any]:
        """Complete workflow: create Firestore record and publish message

        Args:
            book_id: Unique book identifier
            title: Book title
            author: Book author (optional)
            language: Book language (default: vi-VN)
            pdf_path: Path to PDF file in GCS

        Returns:
            Dictionary with operation results
        """
        try:
            logger.info(f"Starting PDF processing trigger for book: {book_id}")
            logger.info(f"Using Firestore project: {self.firestore_project_id}")

            # Step 1: Create Firestore record and verify it
            firestore_data = self.create_firestore_record(book_id, title, author, language, pdf_path)

            # Step 2: Publish processing message
            message_id = self.publish_pdf_processing_message(book_id, title, author, language, pdf_path)

            result = {
                'status': 'success',
                'book_id': book_id,
                'message_id': message_id,
                'firestore_created': True,
                'firestore_verified': True,
                'firestore_project': self.firestore_project_id,
                'firestore_data': firestore_data,
                'message_published': True
            }

            logger.info(f"Successfully triggered PDF processing for book: {book_id}")
            return result

        except Exception as e:
            logger.error(f"Failed to trigger PDF processing: {e}")
            return {
                'status': 'error',
                'book_id': book_id,
                'firestore_project': self.firestore_project_id,
                'error': str(e)
            }


def main():
    """Main function to handle command line arguments and execute processing"""
    parser = argparse.ArgumentParser(description='Trigger PDF processing manually')
    parser.add_argument('--book-id', required=True, help='Unique book identifier')
    parser.add_argument('--title', required=True, help='Book title')
    parser.add_argument('--author', help='Book author (optional)')
    parser.add_argument('--language', default='vi-VN', help='Book language (default: vi-VN)')
    parser.add_argument('--pdf-path', help='Path to PDF file in GCS (optional, will use default pattern)')
    parser.add_argument('--project-id', help='GCP project ID (overrides environment variable)')
    parser.add_argument('--firestore-project-id', help='Firestore project ID (overrides environment variable)')
    parser.add_argument('--topic-name', help='PDF processing topic name (overrides environment variable)')
    
    args = parser.parse_args()
    
    # Get configuration from environment variables or command line
    project_id = args.project_id or os.environ.get('GCP_PROJECT')
    firestore_project_id = args.firestore_project_id or os.environ.get('FIRESTORE_PROJECT_ID') or project_id
    topic_name = args.topic_name or os.environ.get('PDF_PROCESSING_TOPIC')
    
    # Validate required configuration
    if not project_id:
        logger.error("GCP_PROJECT environment variable or --project-id argument required")
        sys.exit(1)
    
    if not topic_name:
        logger.error("PDF_PROCESSING_TOPIC environment variable or --topic-name argument required")
        sys.exit(1)
    
    try:
        # Initialize trigger
        trigger = PDFProcessingTrigger(project_id, firestore_project_id, topic_name)
        
        # Execute processing
        result = trigger.trigger_processing(
            book_id=args.book_id,
            title=args.title,
            author=args.author,
            language=args.language,
            pdf_path=args.pdf_path
        )
        
        # Print result (convert timestamps to strings for JSON serialization)
        result_for_print = result.copy()
        if 'firestore_data' in result_for_print:
            firestore_data = result_for_print['firestore_data'].copy()
            for key, value in firestore_data.items():
                if hasattr(value, 'isoformat'):  # Handle datetime objects
                    firestore_data[key] = value.isoformat()
            result_for_print['firestore_data'] = firestore_data

        print(json.dumps(result_for_print, indent=2))
        
        if result['status'] == 'success':
            logger.info("PDF processing triggered successfully!")
            sys.exit(0)
        else:
            logger.error("Failed to trigger PDF processing")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Critical error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
