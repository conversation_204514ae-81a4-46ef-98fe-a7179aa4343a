terraform {
  required_version = ">= 1.5"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }

  # TODO: Configure backend for state management
  # backend "gcs" {
  #   bucket  = "your-terraform-state-bucket"
  #   prefix  = "terraform/state/dev"
  # }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# Reference to existing data bucket
data "google_storage_bucket" "data_bucket" {
  name = var.data_bucket_name
}

# Pub/Sub for chapter processing
module "pubsub" {
  source = "../../modules/pubsub"

  project_id                   = var.project_id
  environment                  = var.environment
  pdf_processing_topic_name    = var.pdf_processing_topic_name
  chapter_topic_name           = var.chapter_topic_name
  dead_letter_topic_name       = var.dead_letter_topic_name
  max_delivery_attempts        = var.max_delivery_attempts
}

# Firestore database
module "firestore" {
  source = "../../modules/firestore"
  
  project_id = var.project_id
  location   = var.firestore_location
}

# Secrets for API keys
module "secrets" {
  source = "../../modules/secret"
  
  project_id  = var.project_id
  environment = var.environment
  secrets = {
    gemini_api_key = {
      secret_id = "gemini-api-key"
    }
  }
}

# IAM service accounts and roles
module "iam" {
  source = "../../modules/iam"
  
  project_id         = var.project_id
  environment        = var.environment
  data_bucket_name   = data.google_storage_bucket.data_bucket.name
  chapter_topic_name = module.pubsub.chapter_topic_name
  gemini_secret_name = module.secrets.secret_names["gemini_api_key"]
}

# PDF to Text Cloud Function
module "pdf_to_text_function" {
  source = "../../modules/cloud_function"
  
  project_id       = var.project_id
  region           = var.region
  environment      = var.environment
  function_name    = "pdf-to-text"
  description      = "Processes uploaded PDFs and extracts chapter text"
  runtime          = "python312"
  
  # Function configuration
  memory            = var.pdf_function_memory
  timeout           = var.pdf_function_timeout
  max_instances     = var.pdf_function_max_instances
  min_instances     = var.pdf_function_min_instances
  concurrency       = var.pdf_function_concurrency
  
  # Source code
  source_dir = "../../../functions/pdf-to-text"
  entry_point = "process_pdf_request"
  
  # Environment variables
  environment_variables = {
    ENVIRONMENT          = var.environment
    DATA_BUCKET_NAME     = data.google_storage_bucket.data_bucket.name
    CHAPTER_TOPIC_NAME   = module.pubsub.chapter_topic_name
  }
  
  # Secret environment variables
  secret_environment_variables = [
    {
      key        = "GEMINI_API_KEY"
      project_id = var.project_id
      secret     = module.secrets.secret_names["gemini_api_key"]
      version    = "latest"
    }
  ]
  
  # Pub/Sub trigger for PDF processing
  trigger_config = {
    event_type   = "google.cloud.pubsub.topic.v1.messagePublished"
    resource     = module.pubsub.pdf_processing_topic_id
    retry_policy = "RETRY_POLICY_RETRY"
  }
  
  service_account_email = module.iam.pdf_function_sa_email
  
  depends_on = [
    data.google_storage_bucket.data_bucket,
    module.pubsub,
    module.iam
  ]
}

# Chapter TTS Cloud Function
module "chapter_tts_function" {
  source = "../../modules/cloud_function"
  
  project_id       = var.project_id
  region           = var.region
  environment      = var.environment
  function_name    = "chapter-tts"
  description      = "Processes chapter text and generates audio files"
  runtime          = "python312"
  
  # Function configuration
  memory            = var.tts_function_memory
  timeout           = var.tts_function_timeout
  max_instances     = var.tts_function_max_instances
  min_instances     = var.tts_function_min_instances
  concurrency       = var.tts_function_concurrency
  
  # Source code
  source_dir = "../../../functions/chapter-tts"
  entry_point = "process_chapter_tts"
  use_docker = true
  
  # Environment variables
  environment_variables = {
    ENVIRONMENT          = var.environment
    DATA_BUCKET_NAME     = data.google_storage_bucket.data_bucket.name
  }
  
  # Pub/Sub trigger
  trigger_config = {
    event_type   = "google.cloud.pubsub.message.v1.published"
    resource     = module.pubsub.chapter_subscription_name
    retry_policy = "RETRY_POLICY_RETRY"
  }
  
  service_account_email = module.iam.tts_function_sa_email
  
  depends_on = [
    module.pubsub,
    module.iam
  ]
}

# Monitoring and alerting (commented out for initial deployment)
# module "monitoring" {
#   source = "../../modules/monitoring"
#   
#   project_id                = var.project_id
#   environment               = var.environment
#   pdf_function_name         = module.pdf_to_text_function.function_name
#   tts_function_name         = module.chapter_tts_function.function_name
#   chapter_subscription_name = module.pubsub.chapter_subscription_name
#   dead_letter_topic_name    = module.pubsub.dead_letter_topic_name
#   notification_channels     = var.notification_channels
# }