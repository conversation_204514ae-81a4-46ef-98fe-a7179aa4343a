output "project_id" {
  description = "The GCP project ID"
  value       = var.project_id
}

output "region" {
  description = "The GCP region"
  value       = var.region
}

output "environment" {
  description = "The environment name"
  value       = var.environment
}

# Storage outputs
output "data_bucket_name" {
  description = "Name of the data storage bucket"
  value       = data.google_storage_bucket.data_bucket.name
}

output "data_bucket_url" {
  description = "URL of the data storage bucket"
  value       = data.google_storage_bucket.data_bucket.url
}

# Pub/Sub outputs
output "chapter_topic_name" {
  description = "Name of the chapter processing topic"
  value       = module.pubsub.chapter_topic_name
}

output "chapter_subscription_name" {
  description = "Name of the chapter processing subscription"
  value       = module.pubsub.chapter_subscription_name
}

output "dead_letter_topic_name" {
  description = "Name of the dead letter topic"
  value       = module.pubsub.dead_letter_topic_name
}

# Function outputs
output "pdf_to_text_function_name" {
  description = "Name of the PDF to text processing function"
  value       = module.pdf_to_text_function.function_name
}

output "pdf_to_text_function_uri" {
  description = "URI of the PDF to text processing function"
  value       = module.pdf_to_text_function.function_uri
}

output "chapter_tts_function_name" {
  description = "Name of the chapter TTS function"
  value       = module.chapter_tts_function.function_name
}

output "chapter_tts_function_uri" {
  description = "URI of the chapter TTS function"
  value       = module.chapter_tts_function.function_uri
}

# Service account outputs
output "pdf_function_sa_email" {
  description = "Email of the PDF processing function service account"
  value       = module.iam.pdf_function_sa_email
}

output "tts_function_sa_email" {
  description = "Email of the TTS function service account"
  value       = module.iam.tts_function_sa_email
}

output "admin_publisher_sa_email" {
  description = "Email of the admin publisher service account"
  value       = module.iam.admin_publisher_sa_email
}

# Secret outputs
output "gemini_secret_name" {
  description = "Name of the Gemini API key secret"
  value       = module.secrets.secret_names["gemini_api_key"]
}