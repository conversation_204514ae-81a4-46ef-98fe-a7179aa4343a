# GCP Project Configuration
project_id = "fonos-audio"
region     = "asia-southeast1"

# Environment
environment = "prod"

# Storage Configuration
data_bucket_name = "fonos-production"  # Using existing bucket

# Pub/Sub Configuration
chapter_topic_name       = "tts-prod-chapter-process"
dead_letter_topic_name   = "tts-prod-dlq"
max_delivery_attempts    = 5

# Firestore Configuration
firestore_location   = "asia-southeast1"
firestore_project_id = "fonos-audio"  # Production environment uses fonos-audio project

# PDF to Text Function Configuration
pdf_function_memory        = "4096Mi"
pdf_function_timeout       = "1800s"  # 30 minutes for large PDFs
pdf_function_max_instances = 20        # Higher for prod
pdf_function_min_instances = 1         # Reduce cold starts
pdf_function_concurrency   = 1

# Chapter TTS Function Configuration
tts_function_memory        = "2048Mi"
tts_function_timeout       = "540s"  # 9 minutes
tts_function_max_instances = 100      # Higher for prod
tts_function_min_instances = 2        # Reduce cold starts
tts_function_concurrency   = 5

# Monitoring Configuration
notification_channels = []  # TODO: Add production notification channel IDs

# Labels
labels = {
  environment = "prod"
  project     = "book-tts"
  managed_by  = "terraform"
}