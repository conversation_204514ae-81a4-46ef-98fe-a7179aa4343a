#!/bin/bash

# Test System Script
# This script tests the complete TTS system end-to-end

set -e

# Configuration
ENVIRONMENT="${1:-dev}"
PROJECT_ID="${TF_VAR_project_id:-}"
REGION="${REGION:-asia-southeast1}"
TEST_BOOK_ID="${TEST_BOOK_ID:-test-book-$(date +%s)}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_step "Checking prerequisites..."
    
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed."
        exit 1
    fi
    
    if ! command -v gsutil &> /dev/null; then
        print_error "gsutil is not installed."
        exit 1
    fi
    
    if [[ -z "$PROJECT_ID" ]]; then
        print_error "PROJECT_ID not set. Export TF_VAR_project_id."
        exit 1
    fi
    
    if [[ ! -d "envs/$ENVIRONMENT" ]]; then
        print_error "Environment '$ENVIRONMENT' not found."
        exit 1
    fi
    
    print_info "Prerequisites check passed."
}

# Get deployment information
get_deployment_info() {
    print_step "Getting deployment information..."
    
    cd "envs/$ENVIRONMENT"
    
    # Check if terraform state exists
    if ! terraform show &> /dev/null; then
        print_error "No Terraform state found. Deploy infrastructure first."
        exit 1
    fi
    
    DATA_BUCKET=$(terraform output -raw data_bucket_name 2>/dev/null || echo "")
    PDF_FUNCTION_NAME=$(terraform output -raw pdf_to_text_function_name 2>/dev/null || echo "pdf-to-text")
    TTS_FUNCTION_NAME=$(terraform output -raw chapter_tts_function_name 2>/dev/null || echo "chapter-tts")
    CHAPTER_TOPIC=$(terraform output -raw chapter_topic_name 2>/dev/null || echo "tts-${ENVIRONMENT}-chapter-process")
    
    if [[ -z "$DATA_BUCKET" ]]; then
        print_error "Could not get data bucket name from Terraform outputs."
        exit 1
    fi
    
    cd ../..
    
    print_info "Data Bucket: $DATA_BUCKET"
    print_info "PDF Function: $PDF_FUNCTION_NAME"
    print_info "TTS Function: $TTS_FUNCTION_NAME"
    print_info "Chapter Topic: $CHAPTER_TOPIC"
}

# Create test PDF
create_test_pdf() {
    print_step "Creating test PDF..."
    
    # Create a simple test PDF using a basic text file and pandoc if available
    if command -v pandoc &> /dev/null; then
        cat > /tmp/test-book.md << 'EOF'
# Test Book

## Chapter 1: Introduction

This is the introduction chapter of our test book. It contains some sample text to test the PDF processing functionality.

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.

## Chapter 2: Main Content

This is the main content chapter. It demonstrates how the system processes multiple chapters from a single PDF document.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

## Chapter 3: Conclusion

This concludes our test book. The system should extract this text and convert it to audio.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis.
EOF
        
        pandoc /tmp/test-book.md -o /tmp/test-book.pdf
        print_info "Test PDF created using pandoc."
    else
        print_warning "pandoc not available. Please provide a test PDF manually."
        print_info "Expected path: /tmp/test-book.pdf"
        print_info "Or set TEST_PDF_PATH environment variable."
        
        # Ask user to provide PDF path
        if [[ -z "${TEST_PDF_PATH:-}" ]]; then
            read -p "Enter path to test PDF file: " TEST_PDF_PATH
        fi
        
        if [[ -n "$TEST_PDF_PATH" && -f "$TEST_PDF_PATH" ]]; then
            cp "$TEST_PDF_PATH" /tmp/test-book.pdf
            print_info "Using provided PDF: $TEST_PDF_PATH"
        else
            print_error "No valid test PDF provided."
            exit 1
        fi
    fi
}

# Upload test PDF
upload_test_pdf() {
    print_step "Uploading test PDF to GCS..."
    
    GCS_PATH="gs://$DATA_BUCKET/books/$TEST_BOOK_ID/book.pdf"
    
    if [[ ! -f "/tmp/test-book.pdf" ]]; then
        print_error "Test PDF not found at /tmp/test-book.pdf"
        exit 1
    fi
    
    gsutil cp /tmp/test-book.pdf "$GCS_PATH"
    print_info "PDF uploaded to: $GCS_PATH"
    
    # Clean up local file
    rm -f /tmp/test-book.pdf /tmp/test-book.md
}

# Monitor PDF processing
monitor_pdf_processing() {
    print_step "Monitoring PDF processing..."
    
    print_info "Waiting for PDF processing to start..."
    sleep 5
    
    # Check function logs
    print_info "Recent PDF function logs:"
    gcloud functions logs read "$PDF_FUNCTION_NAME" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --limit=20 \
        --format="value(timestamp,textPayload)" 2>/dev/null || echo "No logs yet"
    
    # Wait for processing
    print_info "Waiting for chapter extraction (this may take 2-3 minutes)..."
    sleep 30
    
    # Check for extracted chapters
    print_info "Checking for extracted chapters..."
    CHAPTER_COUNT=$(gsutil ls "gs://$DATA_BUCKET/books/$TEST_BOOK_ID/text-extracted-chapters/" 2>/dev/null | wc -l || echo "0")
    print_info "Found $CHAPTER_COUNT chapter files."
    
    if [[ "$CHAPTER_COUNT" -gt 0 ]]; then
        print_info "✅ PDF processing successful! Chapters extracted."
        gsutil ls "gs://$DATA_BUCKET/books/$TEST_BOOK_ID/text-extracted-chapters/"
    else
        print_warning "No chapters found yet. Check function logs for errors."
    fi
}

# Monitor TTS processing
monitor_tts_processing() {
    print_step "Monitoring TTS processing..."
    
    print_info "Waiting for TTS processing to start..."
    sleep 10
    
    # Check TTS function logs
    print_info "Recent TTS function logs:"
    gcloud functions logs read "$TTS_FUNCTION_NAME" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --limit=10 \
        --format="value(timestamp,textPayload)" 2>/dev/null || echo "No logs yet"
    
    # Wait for audio generation
    print_info "Waiting for audio generation (this may take 3-5 minutes)..."
    sleep 60
    
    # Check for generated audio
    print_info "Checking for generated audio files..."
    AUDIO_COUNT=$(gsutil ls "gs://$DATA_BUCKET/books/$TEST_BOOK_ID/audio/" 2>/dev/null | wc -l || echo "0")
    print_info "Found $AUDIO_COUNT audio files."
    
    if [[ "$AUDIO_COUNT" -gt 0 ]]; then
        print_info "✅ TTS processing successful! Audio files generated."
        gsutil ls -l "gs://$DATA_BUCKET/books/$TEST_BOOK_ID/audio/"
    else
        print_warning "No audio files found yet. Check function logs for errors."
    fi
}

# Check Firestore data
check_firestore() {
    print_step "Checking Firestore data..."
    
    # Note: This requires firestore admin SDK or gcloud firestore commands
    print_info "Book document should be available at:"
    print_info "  Collection: books"
    print_info "  Document: $TEST_BOOK_ID"
    print_info ""
    print_info "Chapter documents should be available at:"
    print_info "  Collection: books/$TEST_BOOK_ID/chapters"
    print_info ""
    print_info "Check the Firestore console at:"
    print_info "  https://console.cloud.google.com/firestore/data?project=$PROJECT_ID"
}

# Check Pub/Sub metrics
check_pubsub_metrics() {
    print_step "Checking Pub/Sub metrics..."
    
    print_info "Topic: $CHAPTER_TOPIC"
    print_info ""
    print_info "Check Pub/Sub console for message metrics:"
    print_info "  https://console.cloud.google.com/cloudpubsub/topic/detail/$CHAPTER_TOPIC?project=$PROJECT_ID"
}

# Show test results
show_test_results() {
    print_step "Test Results Summary"
    
    print_info "=== Test Configuration ==="
    echo "Environment: $ENVIRONMENT"
    echo "Project ID: $PROJECT_ID"
    echo "Test Book ID: $TEST_BOOK_ID"
    echo "Data Bucket: $DATA_BUCKET"
    echo ""
    
    print_info "=== Generated Files ==="
    print_info "Checking final file structure..."
    
    # List all files for the test book
    echo "Book manifest:"
    gsutil cat "gs://$DATA_BUCKET/books/$TEST_BOOK_ID/book_manifest.json" 2>/dev/null | jq . || echo "Not found"
    echo ""
    
    echo "Extracted chapters:"
    gsutil ls "gs://$DATA_BUCKET/books/$TEST_BOOK_ID/text-extracted-chapters/" 2>/dev/null || echo "None found"
    echo ""
    
    echo "Generated audio:"
    gsutil ls -l "gs://$DATA_BUCKET/books/$TEST_BOOK_ID/audio/" 2>/dev/null || echo "None found"
    echo ""
    
    print_info "=== Next Steps ==="
    print_info "1. Check function logs for any errors:"
    echo "   gcloud functions logs read --region=$REGION --project=$PROJECT_ID"
    print_info "2. Download and test audio files:"
    echo "   gsutil cp gs://$DATA_BUCKET/books/$TEST_BOOK_ID/audio/* ./audio/"
    print_info "3. Monitor in Cloud Console:"
    echo "   https://console.cloud.google.com/functions/list?project=$PROJECT_ID"
}

# Cleanup test resources
cleanup_test() {
    if [[ "${CLEANUP_TEST:-false}" == "true" ]]; then
        print_step "Cleaning up test resources..."
        
        # Remove test book files
        gsutil -m rm -r "gs://$DATA_BUCKET/books/$TEST_BOOK_ID/" 2>/dev/null || true
        
        print_info "Test resources cleaned up."
    fi
}

# Main execution
main() {
    print_info "Starting end-to-end system test..."
    print_info "Environment: $ENVIRONMENT"
    print_info "Project ID: $PROJECT_ID"
    print_info "Test Book ID: $TEST_BOOK_ID"
    print_info ""
    
    check_prerequisites
    get_deployment_info
    create_test_pdf
    upload_test_pdf
    monitor_pdf_processing
    monitor_tts_processing
    check_firestore
    check_pubsub_metrics
    show_test_results
    cleanup_test
    
    print_info ""
    print_info "✅ End-to-end test completed!"
    print_info ""
    print_info "Review the results above and check the Cloud Console for detailed metrics."
}

# Handle script arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --cleanup)
            CLEANUP_TEST=true
            shift
            ;;
        --test-pdf)
            TEST_PDF_PATH="$2"
            shift 2
            ;;
        --book-id)
            TEST_BOOK_ID="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [ENVIRONMENT] [OPTIONS]"
            echo ""
            echo "Arguments:"
            echo "  ENVIRONMENT    Environment to test (dev, staging, prod). Default: dev"
            echo ""
            echo "Options:"
            echo "  --cleanup      Clean up test resources after completion"
            echo "  --test-pdf     Path to custom test PDF file"
            echo "  --book-id      Custom test book ID"
            echo "  -h, --help     Show this help message"
            echo ""
            echo "Environment variables:"
            echo "  TF_VAR_project_id   GCP project ID (required)"
            echo "  REGION              GCP region (default: asia-southeast1)"
            echo "  TEST_PDF_PATH       Path to test PDF file"
            echo "  CLEANUP_TEST        Set to 'true' to cleanup after test"
            echo ""
            echo "Examples:"
            echo "  $0 dev                    # Test dev environment"
            echo "  $0 dev --cleanup          # Test and cleanup"
            echo "  $0 staging --test-pdf my-book.pdf  # Use custom PDF"
            exit 0
            ;;
        dev|staging|prod)
            ENVIRONMENT="$1"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Change to terraform directory
cd "$(dirname "$0")"

# Execute main function
main