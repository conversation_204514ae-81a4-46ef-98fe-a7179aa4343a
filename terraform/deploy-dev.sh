#!/bin/bash

# Deploy Development Environment Script
# This script deploys the TTS system infrastructure to the dev environment

set -e

# Configuration
ENVIRONMENT="dev"
PROJECT_ID="${TF_VAR_project_id:-fonos-audio}"
REGION="${REGION:-asia-southeast1}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install it first."
        exit 1
    fi
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        print_error "Not authenticated with gcloud. Run 'gcloud auth login' first."
        exit 1
    fi
    
    # Check if project ID is set
    if [[ -z "$PROJECT_ID" ]]; then
        print_error "PROJECT_ID not set. Export TF_VAR_project_id or set PROJECT_ID environment variable."
        print_info "Example: export TF_VAR_project_id=your-gcp-project-id"
        exit 1
    fi
    
    print_info "Prerequisites check passed."
}

# Enable required APIs
enable_apis() {
    print_step "Enabling required GCP APIs..."
    
    apis=(
        "cloudfunctions.googleapis.com"
        "run.googleapis.com"
        "eventarc.googleapis.com"
        "cloudbuild.googleapis.com"
        "pubsub.googleapis.com"
        "firestore.googleapis.com"
        "secretmanager.googleapis.com"
        "storage.googleapis.com"
        "logging.googleapis.com"
        "monitoring.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        print_info "Enabling $api..."
        gcloud services enable "$api" --project="$PROJECT_ID" --quiet
    done
    
    print_info "APIs enabled successfully."
}

# Initialize Terraform
init_terraform() {
    print_step "Initializing Terraform..."
    
    cd "envs/$ENVIRONMENT"
    
    # Initialize Terraform
    terraform init
    
    print_info "Terraform initialized successfully."
}

# Plan deployment
plan_deployment() {
    print_step "Planning Terraform deployment..."
    
    # Create a plan file
    terraform plan -out="terraform.tfplan" -var-file="terraform.tfvars"
    
    print_info "Terraform plan completed. Review the changes above."
    
    # Ask for confirmation unless in non-interactive mode
    if [[ "${NON_INTERACTIVE:-false}" != "true" ]]; then
        read -p "Do you want to proceed with the deployment? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_warning "Deployment cancelled by user."
            exit 0
        fi
    fi
}

# Apply deployment
apply_deployment() {
    print_step "Applying Terraform configuration..."
    
    # Apply the plan
    terraform apply "terraform.tfplan"
    
    print_info "Terraform deployment completed successfully."
    
    # Clean up plan file
    rm -f terraform.tfplan
}

# Set up secrets
setup_secrets() {
    print_step "Setting up secrets..."
    
    # Check if GEMINI_API_KEY is set
    if [[ -z "${GEMINI_API_KEY:-}" ]]; then
        print_warning "GEMINI_API_KEY environment variable not set."
        print_info "Please set your Gemini API key manually:"
        print_info "  gcloud secrets versions add gemini-api-key --data-file=- --project=$PROJECT_ID"
        print_info "  (then paste your API key and press Ctrl+D)"
        return
    fi
    
    # Create secret version
    print_info "Adding Gemini API key to Secret Manager..."
    echo -n "$GEMINI_API_KEY" | gcloud secrets versions add gemini-api-key --data-file=- --project="$PROJECT_ID"
    
    print_info "Secret created successfully."
}

# Deploy function code
deploy_functions() {
    print_step "Deploying Cloud Functions code..."
    
    # Sync TTS processor submodule for chapter-tts function
    print_info "Syncing TTS processor submodule..."
    cd ../../../functions/chapter-tts
    ./build.sh
    
    cd ../..
    
    # Get function names from Terraform outputs
    cd ../terraform/envs/$ENVIRONMENT
    PDF_FUNCTION_NAME=$(terraform output -raw pdf_to_text_function_name 2>/dev/null || echo "pdf-to-text")
    TTS_FUNCTION_NAME=$(terraform output -raw chapter_tts_function_name 2>/dev/null || echo "chapter-tts")
    
    cd ../../../functions
    
    print_info "Deploying PDF processing function: $PDF_FUNCTION_NAME"
    gcloud functions deploy "$PDF_FUNCTION_NAME" \
        --gen2 \
        --runtime=python312 \
        --region="$REGION" \
        --source=pdf-to-text \
        --entry-point=process_gcs_file \
        --project="$PROJECT_ID" \
        --quiet
    
    print_info "Deploying TTS processing function: $TTS_FUNCTION_NAME"
    gcloud functions deploy "$TTS_FUNCTION_NAME" \
        --gen2 \
        --region="$REGION" \
        --source=chapter-tts \
        --entry-point=process_chapter_tts \
        --project="$PROJECT_ID" \
        --quiet
    
    print_info "Functions deployed successfully."
}

# Display outputs
show_outputs() {
    print_step "Deployment outputs..."
    
    cd ../terraform/envs/$ENVIRONMENT
    
    print_info "=== Deployment Summary ==="
    echo "Environment: $ENVIRONMENT"
    echo "Project ID: $PROJECT_ID"
    echo "Region: $REGION"
    echo ""
    
    # Show Terraform outputs
    terraform output
    
    print_info "=== Next Steps ==="
    print_info "1. Upload a PDF to test:"
    echo "   gsutil cp your-book.pdf gs://\$(terraform output -raw data_bucket_name)/books/test-book-001/book.pdf"
    print_info "2. Monitor logs:"
    echo "   gcloud functions logs read --region=$REGION --project=$PROJECT_ID"
    print_info "3. Check Firestore console:"
    echo "   https://console.cloud.google.com/firestore/data?project=$PROJECT_ID"
}

# Main execution
main() {
    print_info "Starting deployment of TTS system to $ENVIRONMENT environment..."
    print_info "Project ID: $PROJECT_ID"
    print_info "Region: $REGION"
    print_info ""
    
    check_prerequisites
    enable_apis
    init_terraform
    plan_deployment
    apply_deployment
    setup_secrets
    deploy_functions
    show_outputs
    
    print_info ""
    print_info "✅ Deployment completed successfully!"
    print_info ""
    print_info "Your TTS system is now ready for testing."
}

# Handle script arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --non-interactive)
            NON_INTERACTIVE=true
            shift
            ;;
        --skip-apis)
            SKIP_APIS=true
            shift
            ;;
        --skip-functions)
            SKIP_FUNCTIONS=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --non-interactive    Run without user prompts"
            echo "  --skip-apis         Skip API enablement"
            echo "  --skip-functions    Skip function deployment"
            echo "  -h, --help          Show this help message"
            echo ""
            echo "Environment variables:"
            echo "  TF_VAR_project_id   GCP project ID (required)"
            echo "  GEMINI_API_KEY      Gemini API key (optional)"
            echo "  REGION              GCP region (default: asia-southeast1)"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Skip API enablement if requested
if [[ "${SKIP_APIS:-false}" == "true" ]]; then
    enable_apis() {
        print_info "Skipping API enablement (--skip-apis flag set)"
    }
fi

# Skip function deployment if requested
if [[ "${SKIP_FUNCTIONS:-false}" == "true" ]]; then
    deploy_functions() {
        print_info "Skipping function deployment (--skip-functions flag set)"
    }
fi

# Change to terraform directory
cd "$(dirname "$0")"

# Execute main function
main "$@"