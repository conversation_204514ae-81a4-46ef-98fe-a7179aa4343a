# Book TTS Terraform Infrastructure

This directory contains Terraform configurations for deploying and managing the Book Text-to-Speech system infrastructure on Google Cloud Platform.

## Project Structure

```
terraform/
├── envs/                    # Environment-specific configurations
│   ├── dev/
│   ├── staging/
│   └── prod/
├── modules/                 # Reusable Terraform modules
│   ├── gcs_bucket/         # GCS bucket configuration
│   ├── pubsub/             # Pub/Sub topics and subscriptions
│   ├── firestore/          # Firestore database setup
│   ├── cloud_function/     # Cloud Functions Gen 2
│   ├── iam/                # Service accounts and IAM roles
│   ├── secret/             # Secret Manager configuration
│   └── monitoring/         # Monitoring and alerting
└── README.md
```

## Prerequisites

1. **Google Cloud Platform Setup**:
   - GCP project with billing enabled
   - APIs enabled: Cloud Functions, Pub/Sub, Firestore, Secret Manager, Cloud Storage
   - Service account with appropriate permissions for Terraform

2. **Terraform Setup**:
   - Terraform >= 1.5 installed
   - Google Cloud SDK installed and authenticated
   - Backend storage bucket for Terraform state (recommended)

3. **Environment Variables**:
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account-key.json"
   export TF_VAR_project_id="your-gcp-project-id"
   ```

## Infrastructure Components

### Core Services
- **GCS Bucket**: Single bucket per environment for PDFs, extracted text, and audio files
- **Pub/Sub**: Message queue for chapter processing with dead letter queue
- **Firestore**: Native mode database for metadata and processing status
- **Cloud Functions Gen 2**: Two functions for PDF processing and TTS generation
- **Secret Manager**: Secure storage for API keys
- **IAM**: Least-privilege service accounts and role bindings

### Security Features
- Uniform bucket-level access with public access prevention
- Least-privilege IAM roles scoped to specific resources
- Secrets managed via Secret Manager (never in code)
- VPC connector support (optional)
- Customer-managed encryption keys (optional)

## Deployment Instructions

### 1. Configure Backend (Recommended)

First, create a GCS bucket for Terraform state:

```bash
gsutil mb gs://your-terraform-state-bucket
gsutil versioning set on gs://your-terraform-state-bucket
```

Update the backend configuration in each environment's `main.tf`:

```hcl
terraform {
  backend "gcs" {
    bucket = "your-terraform-state-bucket"
    prefix = "terraform/state/dev"  # Change per environment
  }
}
```

### 2. Configure Environment Variables

Copy and customize the terraform.tfvars file for your target environment:

```bash
cd terraform/envs/dev
cp terraform.tfvars terraform.tfvars.local
# Edit terraform.tfvars.local with your actual values
```

**Important**: Update these values:
- `project_id`: Your GCP project ID
- `data_bucket_name`: Must be globally unique
- Notification channels for alerts (optional)

### 3. Deploy Infrastructure

```bash
cd terraform/envs/dev

# Initialize Terraform
terraform init

# Review the planned changes
terraform plan -var-file="terraform.tfvars.local"

# Apply the configuration
terraform apply -var-file="terraform.tfvars.local"
```

### 4. Set Up Secrets

After deployment, add the Gemini API key to Secret Manager:

```bash
echo -n "your-gemini-api-key" | gcloud secrets versions add gemini-api-key --data-file=-
```

### 5. Deploy Function Code

The Cloud Functions are created but require code deployment:

```bash
cd functions/

# Deploy PDF processing function
gcloud functions deploy pdf-to-text \
  --gen2 \
  --runtime=python312 \
  --region=asia-southeast1 \
  --source=. \
  --entry-point=process_gcs_file

# Deploy TTS function
gcloud functions deploy chapter-tts \
  --gen2 \
  --runtime=python312 \
  --region=asia-southeast1 \
  --source=. \
  --entry-point=process_chapter_tts
```

## Environment Management

### Development
- Cost-optimized configuration
- Min instances: 0 (cold starts acceptable)
- Reduced monitoring and alerting

### Staging
- Production-like configuration for testing
- Full monitoring and alerting enabled

### Production
- High availability configuration
- Min instances > 0 to reduce cold starts
- Comprehensive monitoring and alerting
- Enhanced security policies

## Monitoring and Alerting

The infrastructure includes:

- **Error Rate Alerts**: Function error rates exceeding thresholds
- **Backlog Alerts**: Pub/Sub subscription message age/count
- **Dead Letter Queue Alerts**: Any messages in DLQ
- **Custom Dashboards**: Function metrics, latency, and throughput

## Maintenance

### Updating Infrastructure
```bash
terraform plan -var-file="terraform.tfvars.local"
terraform apply -var-file="terraform.tfvars.local"
```

### Managing Secrets
```bash
# Rotate API key
gcloud secrets versions add gemini-api-key --data-file=-

# Update functions to use latest version (automatic with "latest")
```

### Scaling Configuration
Update function configurations in `terraform.tfvars.local`:
- `*_function_max_instances`: Control cost and concurrency
- `*_function_memory`: Adjust based on workload requirements
- `*_function_timeout`: Set appropriate timeouts

## Troubleshooting

### Common Issues

1. **Bucket name conflicts**: Ensure globally unique bucket names
2. **API not enabled**: Enable required GCP APIs in your project
3. **Permissions**: Verify Terraform service account has required roles
4. **State lock**: If Terraform state is locked, check for running operations

### Useful Commands

```bash
# View Terraform state
terraform state list

# Import existing resources
terraform import google_storage_bucket.example gs://bucket-name

# Destroy infrastructure (use with caution)
terraform destroy -var-file="terraform.tfvars.local"
```

## Cost Optimization

- Set appropriate `min_instances` (0 for dev, >0 for prod)
- Configure lifecycle policies for temporary files
- Monitor function invocation costs
- Use appropriate memory allocations for functions
- Set max_instances to prevent runaway costs

## Security Considerations

- Never commit API keys or secrets to version control
- Use IAM conditions to further restrict access
- Enable audit logging for production environments
- Regularly rotate API keys and service account keys
- Monitor access patterns and unusual activity

## Next Steps

After successful deployment:

1. Test the complete workflow with a sample PDF
2. Configure monitoring dashboards
3. Set up CI/CD for function deployments
4. Implement backup and disaster recovery procedures
5. Document operational runbooks