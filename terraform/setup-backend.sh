#!/bin/bash

# Terraform Backend Setup Script
# This script sets up GCS backend for Terraform state management

set -e

# Configuration
PROJECT_ID="${TF_VAR_project_id:-}"
BACKEND_BUCKET_NAME="${BACKEND_BUCKET_NAME:-${PROJECT_ID}-terraform-state}"
REGION="${REGION:-asia-southeast1}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        print_error "Not authenticated with gcloud. Run 'gcloud auth login' first."
        exit 1
    fi
    
    # Check if project ID is set
    if [[ -z "$PROJECT_ID" ]]; then
        print_error "PROJECT_ID not set. Export TF_VAR_project_id or set PROJECT_ID environment variable."
        exit 1
    fi
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install it first."
        exit 1
    fi
    
    print_info "Prerequisites check passed."
}

# Create backend bucket
setup_backend_bucket() {
    print_info "Setting up Terraform state backend bucket..."
    
    # Check if bucket already exists
    if gsutil ls -b gs://$BACKEND_BUCKET_NAME &> /dev/null; then
        print_warning "Bucket $BACKEND_BUCKET_NAME already exists."
    else
        print_info "Creating backend bucket: $BACKEND_BUCKET_NAME"
        gsutil mb -p $PROJECT_ID -c STANDARD -l $REGION gs://$BACKEND_BUCKET_NAME
    fi
    
    # Enable versioning
    print_info "Enabling versioning on backend bucket..."
    gsutil versioning set on gs://$BACKEND_BUCKET_NAME
    
    # Set uniform bucket-level access
    print_info "Setting uniform bucket-level access..."
    gsutil uniformbucketlevelaccess set on gs://$BACKEND_BUCKET_NAME
    
    # Prevent public access
    print_info "Preventing public access..."
    gsutil pap set enforced gs://$BACKEND_BUCKET_NAME
    
    # Set lifecycle policy to delete old state versions
    cat > /tmp/lifecycle.json << EOF
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "Delete"},
        "condition": {
          "age": 90,
          "numNewerVersions": 5
        }
      }
    ]
  }
}
EOF
    
    gsutil lifecycle set /tmp/lifecycle.json gs://$BACKEND_BUCKET_NAME
    rm /tmp/lifecycle.json
    
    print_info "Backend bucket setup completed."
}

# Update Terraform backend configurations
update_backend_configs() {
    print_info "Updating Terraform backend configurations..."
    
    for env in dev staging prod; do
        env_dir="envs/$env"
        main_tf="$env_dir/main.tf"
        
        if [[ -f "$main_tf" ]]; then
            print_info "Updating backend config for $env environment..."
            
            # Create a temporary file with the backend configuration
            sed -i.bak "s|# TODO: Configure backend for state management|# Backend configuration for Terraform state|g" "$main_tf"
            sed -i.bak "s|# backend \"gcs\" {|backend \"gcs\" {|g" "$main_tf"
            sed -i.bak "s|#   bucket  = \"your-terraform-state-bucket\"|  bucket  = \"$BACKEND_BUCKET_NAME\"|g" "$main_tf"
            sed -i.bak "s|#   prefix  = \"terraform/state/.*\"|  prefix  = \"terraform/state/$env\"|g" "$main_tf"
            sed -i.bak "s|# }|}|g" "$main_tf"
            
            # Remove backup file
            rm "${main_tf}.bak"
            
            print_info "Backend configuration updated for $env environment."
        else
            print_warning "main.tf not found for $env environment."
        fi
    done
}

# Initialize Terraform for each environment
init_environments() {
    print_info "Initializing Terraform for each environment..."
    
    for env in dev staging prod; do
        env_dir="envs/$env"
        
        if [[ -d "$env_dir" ]]; then
            print_info "Initializing Terraform for $env environment..."
            
            cd "$env_dir"
            
            # Initialize with backend migration
            terraform init -migrate-state -input=false
            
            cd - > /dev/null
            
            print_info "$env environment initialized successfully."
        else
            print_warning "Environment directory not found: $env_dir"
        fi
    done
}

# Main execution
main() {
    print_info "Starting Terraform backend setup..."
    print_info "Project ID: $PROJECT_ID"
    print_info "Backend Bucket: $BACKEND_BUCKET_NAME"
    print_info "Region: $REGION"
    
    check_prerequisites
    setup_backend_bucket
    update_backend_configs
    init_environments
    
    print_info ""
    print_info "✅ Terraform backend setup completed successfully!"
    print_info ""
    print_info "Next steps:"
    print_info "1. Update terraform.tfvars files in each environment"
    print_info "2. Run 'terraform plan' to review changes"
    print_info "3. Run 'terraform apply' to deploy infrastructure"
    print_info ""
    print_info "Backend bucket: gs://$BACKEND_BUCKET_NAME"
    print_info "State files will be stored at:"
    print_info "  - gs://$BACKEND_BUCKET_NAME/terraform/state/dev"
    print_info "  - gs://$BACKEND_BUCKET_NAME/terraform/state/staging" 
    print_info "  - gs://$BACKEND_BUCKET_NAME/terraform/state/prod"
}

# Execute main function
main "$@"