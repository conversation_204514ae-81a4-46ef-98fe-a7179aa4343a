variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "location" {
  description = "The location for the Firestore database"
  type        = string
  default     = "asia-southeast1"
  validation {
    condition = can(regex("^(nam5|eur3|asia-southeast1|us-central1|us-east1|us-east4|us-west2|us-west3|us-west4|northamerica-northeast1|northamerica-northeast2|southamerica-east1|europe-west1|europe-west2|europe-west3|europe-west4|europe-west6|europe-north1|asia-east1|asia-east2|asia-northeast1|asia-northeast2|asia-northeast3|asia-south1|asia-southeast2|australia-southeast1)$", var.location))
    error_message = "Location must be a valid Firestore location."
  }
}

variable "database_id" {
  description = "The ID of the Firestore database"
  type        = string
  default     = "(default)"
}

variable "create_database" {
  description = "Whether to create a new Firestore database (false to use existing default database)"
  type        = bool
  default     = false
}

variable "concurrency_mode" {
  description = "Concurrency mode for the Firestore database"
  type        = string
  default     = "OPTIMISTIC"
  validation {
    condition = contains([
      "OPTIMISTIC",
      "PESSIMISTIC"
    ], var.concurrency_mode)
    error_message = "Concurrency mode must be either OPTIMISTIC or PESSIMISTIC."
  }
}

variable "app_engine_integration_mode" {
  description = "App Engine integration mode"
  type        = string
  default     = "DISABLED"
  validation {
    condition = contains([
      "ENABLED",
      "DISABLED"
    ], var.app_engine_integration_mode)
    error_message = "App Engine integration mode must be either ENABLED or DISABLED."
  }
}

variable "enable_pitr" {
  description = "Enable point-in-time recovery for the database"
  type        = bool
  default     = true
}

variable "enable_delete_protection" {
  description = "Enable delete protection for the database"
  type        = bool
  default     = true
}

variable "indexes" {
  description = "Map of Firestore indexes to create"
  type = map(object({
    collection = string
    fields = list(object({
      field_path   = string
      order        = optional(string)  # ASC or DESC
      array_config = optional(string)  # CONTAINS
    }))
    query_scope = optional(string)  # COLLECTION or COLLECTION_GROUP
    api_scope   = optional(string)  # ANY_API or DATASTORE_MODE_API
  }))
  default = {}
}

variable "ttl_fields" {
  description = "Map of TTL (Time-to-Live) fields to configure"
  type = map(object({
    collection = string
    field      = string
  }))
  default = {}
}

variable "security_rules_source" {
  description = "Path to Firestore security rules file"
  type        = string
  default     = null
}

variable "enable_backup" {
  description = "Enable automatic backups for Firestore"
  type        = bool
  default     = false
}

variable "backup_retention" {
  description = "Backup retention period (e.g., '604800s' for 7 days)"
  type        = string
  default     = "604800s"  # 7 days
}

variable "backup_schedule" {
  description = "Backup schedule configuration"
  type = object({
    type        = string           # "daily" or "weekly"
    day_of_week = optional(string) # Required for weekly (MONDAY, TUESDAY, etc.)
  })
  default = {
    type = "daily"
  }
}