output "app_engine_application_id" {
  description = "ID of the App Engine application"
  value       = var.project_id
}

output "app_engine_location_id" {
  description = "Location ID of the App Engine application" 
  value       = "asia-south1"  # Using existing App Engine location
}

output "database_name" {
  description = "Name of the Firestore database"
  value       = var.create_database ? google_firestore_database.database[0].name : var.database_id
}

output "database_uid" {
  description = "UID of the Firestore database"
  value       = var.create_database ? google_firestore_database.database[0].uid : null
}

output "database_location_id" {
  description = "Location ID of the Firestore database"
  value       = var.create_database ? google_firestore_database.database[0].location_id : var.location
}

output "database_type" {
  description = "Type of the Firestore database"
  value       = var.create_database ? google_firestore_database.database[0].type : "FIRESTORE_NATIVE"
}

output "index_names" {
  description = "Names of created Firestore indexes"
  value       = [for index in google_firestore_index.indexes : index.name]
}

output "ttl_field_names" {
  description = "Names of configured TTL fields"
  value       = [for field in google_firestore_field.ttl_fields : field.name]
}