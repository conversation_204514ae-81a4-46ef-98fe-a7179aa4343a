terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Use existing App Engine application
data "google_app_engine_default_service_account" "default" {
  project = var.project_id
}

# Enable Firestore API if not already enabled
resource "google_project_service" "firestore" {
  project = var.project_id
  service = "firestore.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy        = false
}

# Firestore Database (Native mode)
resource "google_firestore_database" "database" {
  count = var.create_database ? 1 : 0

  project                           = var.project_id
  name                             = var.database_id
  location_id                      = var.location
  type                             = "FIRESTORE_NATIVE"
  concurrency_mode                 = var.concurrency_mode
  app_engine_integration_mode      = var.app_engine_integration_mode
  point_in_time_recovery_enablement = var.enable_pitr ? "POINT_IN_TIME_RECOVERY_ENABLED" : "POINT_IN_TIME_RECOVERY_DISABLED"
  delete_protection_state          = var.enable_delete_protection ? "DELETE_PROTECTION_ENABLED" : "DELETE_PROTECTION_DISABLED"

  depends_on = [
    google_project_service.firestore
  ]
}

# Firestore Indexes
resource "google_firestore_index" "indexes" {
  for_each = var.indexes

  project    = var.project_id
  collection = each.value.collection
  database   = var.create_database ? google_firestore_database.database[0].name : var.database_id

  dynamic "fields" {
    for_each = each.value.fields
    content {
      field_path   = fields.value.field_path
      order        = lookup(fields.value, "order", null)
      array_config = lookup(fields.value, "array_config", null)
    }
  }

  query_scope = lookup(each.value, "query_scope", "COLLECTION")
  api_scope   = lookup(each.value, "api_scope", "ANY_API")

  depends_on = [google_firestore_database.database]
}

# Firestore TTL Policy (Time-to-Live for automatic document deletion)
resource "google_firestore_field" "ttl_fields" {
  for_each = var.ttl_fields

  project    = var.project_id
  database   = var.create_database ? google_firestore_database.database[0].name : var.database_id
  collection = each.value.collection
  field      = each.value.field

  ttl_config {}

  depends_on = [google_firestore_database.database]
}

# Security Rules (if provided)
# Note: google_firestore_security_rules is not supported in current provider version
# Rules should be managed separately through console or gcloud commands

# Backup Schedule (if enabled)
resource "google_firestore_backup_schedule" "backup_schedule" {
  count = var.enable_backup ? 1 : 0

  project  = var.project_id
  database = var.create_database ? google_firestore_database.database[0].name : var.database_id

  retention = var.backup_retention

  dynamic "daily_recurrence" {
    for_each = var.backup_schedule.type == "daily" ? [var.backup_schedule] : []
    content {}
  }

  dynamic "weekly_recurrence" {
    for_each = var.backup_schedule.type == "weekly" ? [var.backup_schedule] : []
    content {
      day = var.backup_schedule.day_of_week
    }
  }

  depends_on = [google_firestore_database.database]
}