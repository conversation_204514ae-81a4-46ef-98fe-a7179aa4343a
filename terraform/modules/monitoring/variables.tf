variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

# Function names for monitoring
variable "pdf_function_name" {
  description = "Name of the PDF processing function"
  type        = string
}

variable "tts_function_name" {
  description = "Name of the TTS processing function"
  type        = string
}

# Pub/Sub resources for monitoring
variable "chapter_subscription_name" {
  description = "Name of the chapter processing subscription"
  type        = string
}

variable "dead_letter_topic_name" {
  description = "Name of the dead letter topic"
  type        = string
}

# Notification configuration
variable "notification_channels" {
  description = "List of existing notification channel IDs"
  type        = list(string)
  default     = []
}

variable "custom_notification_channels" {
  description = "Custom notification channels to create"
  type = map(object({
    display_name = string
    type         = string  # email, sms, slack, pagerduty, etc.
    labels       = map(string)
    user_labels  = optional(map(string))
    description  = optional(string)
    enabled      = optional(bool)
  }))
  default = {}
}

# Alert thresholds
variable "function_error_rate_threshold" {
  description = "Error rate threshold for function alerts (0.0-1.0)"
  type        = number
  default     = 0.05  # 5%
  validation {
    condition     = var.function_error_rate_threshold >= 0 && var.function_error_rate_threshold <= 1
    error_message = "Error rate threshold must be between 0 and 1."
  }
}

variable "pdf_function_timeout_threshold" {
  description = "Execution time threshold for PDF function alerts (milliseconds)"
  type        = number
  default     = 1500000  # 25 minutes
}

variable "tts_function_timeout_threshold" {
  description = "Execution time threshold for TTS function alerts (milliseconds)"
  type        = number
  default     = 480000  # 8 minutes
}

variable "pubsub_backlog_count_threshold" {
  description = "Number of undelivered messages threshold for Pub/Sub alerts"
  type        = number
  default     = 100
}

variable "pubsub_backlog_age_threshold" {
  description = "Age of oldest undelivered message threshold (seconds)"
  type        = number
  default     = 3600  # 1 hour
}

# Dashboard configuration
variable "enable_dashboard" {
  description = "Enable creation of monitoring dashboard"
  type        = bool
  default     = true
}

variable "dashboard_name" {
  description = "Name of the monitoring dashboard"
  type        = string
  default     = null  # Will use default naming
}

# Uptime checks
variable "uptime_check_configs" {
  description = "Configuration for uptime checks on HTTP functions"
  type = map(object({
    host             = string
    path             = optional(string)
    expected_content = optional(string)
  }))
  default = {}
}

# Log-based metrics configuration
variable "enable_custom_metrics" {
  description = "Enable creation of custom log-based metrics"
  type        = bool
  default     = true
}

variable "custom_log_filters" {
  description = "Custom log filters for creating additional metrics"
  type = map(object({
    filter      = string
    description = string
    value_type  = string  # DOUBLE, INT64, BOOL, STRING
    metric_kind = string  # GAUGE, CUMULATIVE, DELTA
    unit        = optional(string)
    label_extractors = optional(map(string))
  }))
  default = {}
}

# Alert policy configuration
variable "alert_policies_enabled" {
  description = "Enable creation of alert policies"
  type        = bool
  default     = true
}

variable "notification_rate_limit_period" {
  description = "Rate limit period for alert notifications (seconds)"
  type        = string
  default     = "300s"  # 5 minutes
}

variable "alert_auto_close_period" {
  description = "Auto-close period for alerts (seconds)"
  type        = string
  default     = "604800s"  # 7 days
}