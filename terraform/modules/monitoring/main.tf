terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Enable monitoring API
resource "google_project_service" "monitoring" {
  project = var.project_id
  service = "monitoring.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy        = false
}

# Log-based metrics for function errors
resource "google_logging_metric" "function_errors" {
  for_each = toset([var.pdf_function_name, var.tts_function_name])

  name   = "${each.value}-error-rate"
  filter = <<-EOT
    resource.type="cloud_function"
    resource.labels.function_name="${each.value}"
    severity>=ERROR
  EOT

  metric_descriptor {
    metric_kind = "GAUGE"
    value_type  = "DOUBLE"
    unit        = "1"
  }

  value_extractor = "EXTRACT(jsonPayload.error_count)"

  label_extractors = {
    function_name = "EXTRACT(resource.labels.function_name)"
    region        = "EXTRACT(resource.labels.region)"
  }

  depends_on = [google_project_service.monitoring]
}

# Alert Policy: Function Error Rate
resource "google_monitoring_alert_policy" "function_error_rate" {
  for_each = toset([var.pdf_function_name, var.tts_function_name])

  display_name = "${each.value} High Error Rate"
  combiner     = "OR"
  enabled      = true

  conditions {
    display_name = "${each.value} error rate > 5%"

    condition_threshold {
      filter          = "metric.type=\"logging.googleapis.com/user/${each.value}-error-rate\" resource.type=\"cloud_function\""
      duration        = "300s"  # 5 minutes
      comparison      = "COMPARISON_GT"
      threshold_value = 0.05    # 5% error rate

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_MEAN"
        group_by_fields = ["resource.labels.function_name"]
      }

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  notification_channels = var.notification_channels

  alert_strategy {
    notification_rate_limit {
      period = "300s"  # Limit to one notification per 5 minutes
    }
    
    auto_close = "604800s"  # Auto-close after 7 days
  }

  documentation {
    content = "Function ${each.value} is experiencing high error rates. Check function logs for details."
    mime_type = "text/markdown"
  }

  depends_on = [google_logging_metric.function_errors]
}

# Alert Policy: Function Execution Time
resource "google_monitoring_alert_policy" "function_execution_time" {
  for_each = {
    pdf = var.pdf_function_name
    tts = var.tts_function_name
  }

  display_name = "${each.value} Long Execution Time"
  combiner     = "OR"
  enabled      = true

  conditions {
    display_name = "${each.value} execution time > ${each.key == "pdf" ? "25 minutes" : "8 minutes"}"

    condition_threshold {
      filter          = "metric.type=\"cloudfunctions.googleapis.com/function/execution_time\" resource.type=\"cloud_function\" resource.labels.function_name=\"${each.value}\""
      duration        = "60s"
      comparison      = "COMPARISON_GT"
      threshold_value = each.key == "pdf" ? 1500000 : 480000  # 25min for PDF, 8min for TTS (in milliseconds)

      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_MEAN"
        cross_series_reducer = "REDUCE_MAX"
        group_by_fields      = ["resource.labels.function_name"]
      }

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  notification_channels = var.notification_channels

  alert_strategy {
    notification_rate_limit {
      period = "300s"
    }
    auto_close = "3600s"  # Auto-close after 1 hour
  }

  documentation {
    content = "Function ${each.value} is taking longer than expected to execute. This may indicate performance issues or large workloads."
    mime_type = "text/markdown"
  }
}

# Alert Policy: Pub/Sub Subscription Backlog
resource "google_monitoring_alert_policy" "pubsub_backlog" {
  display_name = "Chapter Processing Backlog Alert"
  combiner     = "OR"
  enabled      = true

  conditions {
    display_name = "High number of undelivered messages"

    condition_threshold {
      filter          = "metric.type=\"pubsub.googleapis.com/subscription/num_undelivered_messages\" resource.type=\"pubsub_subscription\" resource.labels.subscription_id=\"${var.chapter_subscription_name}\""
      duration        = "300s"  # 5 minutes
      comparison      = "COMPARISON_GT"
      threshold_value = 100     # More than 100 undelivered messages

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MEAN"
      }

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  conditions {
    display_name = "Old undelivered messages"

    condition_threshold {
      filter          = "metric.type=\"pubsub.googleapis.com/subscription/oldest_unacked_message_age\" resource.type=\"pubsub_subscription\" resource.labels.subscription_id=\"${var.chapter_subscription_name}\""
      duration        = "300s"  # 5 minutes
      comparison      = "COMPARISON_GT"
      threshold_value = 3600    # Messages older than 1 hour

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MAX"
      }

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  notification_channels = var.notification_channels

  alert_strategy {
    notification_rate_limit {
      period = "600s"  # 10 minutes
    }
    auto_close = "7200s"  # Auto-close after 2 hours
  }

  documentation {
    content = <<-EOT
      The chapter processing Pub/Sub subscription has a backlog of messages. This could indicate:
      - TTS function is not processing messages fast enough
      - TTS function is failing to process messages
      - Insufficient function instances to handle the load
    EOT
    mime_type = "text/markdown"
  }
}

# Alert Policy: Dead Letter Queue Messages
resource "google_monitoring_alert_policy" "dead_letter_queue" {
  display_name = "Dead Letter Queue Alert"
  combiner     = "OR"
  enabled      = true

  conditions {
    display_name = "Messages in dead letter queue"

    condition_threshold {
      filter          = "metric.type=\"pubsub.googleapis.com/topic/num_retained_acked_messages\" resource.type=\"pubsub_topic\" resource.labels.topic_id=\"${var.dead_letter_topic_name}\""
      duration        = "60s"
      comparison      = "COMPARISON_GT"
      threshold_value = 0

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MEAN"
      }

      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  notification_channels = var.notification_channels

  alert_strategy {
    notification_rate_limit {
      period = "3600s"  # 1 hour
    }
    auto_close = "86400s"  # Auto-close after 24 hours
  }

  documentation {
    content = <<-EOT
      Messages have been sent to the dead letter queue. This indicates that the chapter processing function has failed multiple times for these messages. Manual investigation is required.
      
      To investigate:
      1. Check the dead letter subscription for message details
      2. Review function logs for error patterns
      3. Manually process or fix the failing messages
    EOT
    mime_type = "text/markdown"
  }
}

# Dashboard for TTS System Monitoring
resource "google_monitoring_dashboard" "tts_dashboard" {
  dashboard_json = jsonencode({
    displayName = "${var.environment} TTS System Dashboard"
    mosaicLayout = {
      tiles = [
        {
          width  = 6
          height = 4
          widget = {
            title = "Function Invocations"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "metric.type=\"cloudfunctions.googleapis.com/function/execution_count\" resource.type=\"cloud_function\""
                      aggregation = {
                        alignmentPeriod    = "60s"
                        perSeriesAligner   = "ALIGN_RATE"
                        crossSeriesReducer = "REDUCE_SUM"
                        groupByFields      = ["resource.labels.function_name"]
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Invocations/sec"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          xPos   = 6
          widget = {
            title = "Function Errors"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "metric.type=\"cloudfunctions.googleapis.com/function/execution_count\" resource.type=\"cloud_function\" metric.labels.status!=\"ok\""
                      aggregation = {
                        alignmentPeriod    = "60s"
                        perSeriesAligner   = "ALIGN_RATE"
                        crossSeriesReducer = "REDUCE_SUM"
                        groupByFields      = ["resource.labels.function_name"]
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Errors/sec"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          yPos   = 4
          widget = {
            title = "Function Execution Time"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "metric.type=\"cloudfunctions.googleapis.com/function/execution_time\" resource.type=\"cloud_function\""
                      aggregation = {
                        alignmentPeriod    = "60s"
                        perSeriesAligner   = "ALIGN_MEAN"
                        crossSeriesReducer = "REDUCE_MEAN"
                        groupByFields      = ["resource.labels.function_name"]
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Execution Time (ms)"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          xPos   = 6
          yPos   = 4
          widget = {
            title = "Pub/Sub Messages"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "metric.type=\"pubsub.googleapis.com/subscription/num_undelivered_messages\" resource.type=\"pubsub_subscription\""
                      aggregation = {
                        alignmentPeriod  = "60s"
                        perSeriesAligner = "ALIGN_MEAN"
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Undelivered Messages"
                scale = "LINEAR"
              }
            }
          }
        }
      ]
    }
  })

  depends_on = [google_project_service.monitoring]
}

# Custom notification channels (if specified)
resource "google_monitoring_notification_channel" "custom_channels" {
  for_each = var.custom_notification_channels

  display_name = each.value.display_name
  type         = each.value.type
  enabled      = lookup(each.value, "enabled", true)

  labels       = each.value.labels
  user_labels  = lookup(each.value, "user_labels", {})
  description  = lookup(each.value, "description", "")

  depends_on = [google_project_service.monitoring]
}

# Uptime check for HTTP functions (if applicable)
resource "google_monitoring_uptime_check_config" "function_uptime" {
  for_each = var.uptime_check_configs

  display_name = "${each.key} Uptime Check"
  timeout      = "10s"
  period       = "300s"  # 5 minutes

  http_check {
    path         = lookup(each.value, "path", "/")
    port         = 443
    use_ssl      = true
    validate_ssl = true

    accepted_response_status_codes {
      status_class = "STATUS_CLASS_2XX"
    }
  }

  monitored_resource {
    type = "uptime_url"
    labels = {
      project_id = var.project_id
      host       = each.value.host
    }
  }

  content_matchers {
    content = lookup(each.value, "expected_content", "")
    matcher = "CONTAINS_STRING"
  }

  depends_on = [google_project_service.monitoring]
}