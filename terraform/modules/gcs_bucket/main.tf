terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# GCS Bucket for data storage
resource "google_storage_bucket" "data_bucket" {
  name                        = var.bucket_name
  location                    = var.location
  project                     = var.project_id
  storage_class               = var.storage_class
  uniform_bucket_level_access = true
  public_access_prevention    = "enforced"

  # Versioning configuration
  versioning {
    enabled = var.enable_versioning
  }

  # Lifecycle configuration
  dynamic "lifecycle_rule" {
    for_each = var.lifecycle_rules
    content {
      action {
        type          = lifecycle_rule.value.action.type
        storage_class = lookup(lifecycle_rule.value.action, "storage_class", null)
      }
      condition {
        age                        = lookup(lifecycle_rule.value.condition, "age", null)
        created_before             = lookup(lifecycle_rule.value.condition, "created_before", null)
        with_state                 = lookup(lifecycle_rule.value.condition, "with_state", null)
        matches_storage_class      = lookup(lifecycle_rule.value.condition, "matches_storage_class", null)
        matches_prefix             = lookup(lifecycle_rule.value.condition, "matches_prefix", null)
        matches_suffix             = lookup(lifecycle_rule.value.condition, "matches_suffix", null)
        num_newer_versions         = lookup(lifecycle_rule.value.condition, "num_newer_versions", null)
        custom_time_before         = lookup(lifecycle_rule.value.condition, "custom_time_before", null)
        days_since_custom_time     = lookup(lifecycle_rule.value.condition, "days_since_custom_time", null)
        days_since_noncurrent_time = lookup(lifecycle_rule.value.condition, "days_since_noncurrent_time", null)
        noncurrent_time_before     = lookup(lifecycle_rule.value.condition, "noncurrent_time_before", null)
      }
    }
  }

  # CORS configuration for web access (optional)
  dynamic "cors" {
    for_each = var.cors_rules
    content {
      origin          = cors.value.origin
      method          = cors.value.method
      response_header = cors.value.response_header
      max_age_seconds = cors.value.max_age_seconds
    }
  }

  # Retention policy (optional)
  dynamic "retention_policy" {
    for_each = var.retention_policy != null ? [var.retention_policy] : []
    content {
      retention_period = retention_policy.value.retention_period
      is_locked        = retention_policy.value.is_locked
    }
  }

  # Customer-managed encryption key (optional)
  dynamic "encryption" {
    for_each = var.kms_key_name != null ? [var.kms_key_name] : []
    content {
      default_kms_key_name = encryption.value
    }
  }

  # Logging configuration (optional)
  dynamic "logging" {
    for_each = var.logging_config != null ? [var.logging_config] : []
    content {
      log_bucket        = logging.value.log_bucket
      log_object_prefix = logging.value.log_object_prefix
    }
  }

  # Website configuration (optional)
  dynamic "website" {
    for_each = var.website_config != null ? [var.website_config] : []
    content {
      main_page_suffix = website.value.main_page_suffix
      not_found_page   = website.value.not_found_page
    }
  }

  # Labels
  labels = merge(
    {
      environment = var.environment
      managed_by  = "terraform"
      purpose     = "tts-data-storage"
    },
    var.labels
  )

  # Prevent accidental deletion in production
  lifecycle {
    prevent_destroy = var.prevent_destroy
  }
}

# Notification configuration for Cloud Functions trigger
resource "google_storage_notification" "function_notification" {
  count = var.enable_notifications ? 1 : 0

  bucket         = google_storage_bucket.data_bucket.name
  payload_format = "JSON_API_V1"
  topic          = var.notification_topic
  event_types    = var.notification_events

  # Filter by object name prefix/suffix if specified
  dynamic "object_name_prefix" {
    for_each = var.notification_object_prefix != null ? [var.notification_object_prefix] : []
    content {
      object_name_prefix = object_name_prefix.value
    }
  }

  depends_on = [google_storage_bucket.data_bucket]
}

# IAM policy for the bucket (if specified)
resource "google_storage_bucket_iam_policy" "bucket_policy" {
  count = var.iam_policy_data != null ? 1 : 0

  bucket      = google_storage_bucket.data_bucket.name
  policy_data = var.iam_policy_data
}

# Individual IAM bindings
resource "google_storage_bucket_iam_binding" "bucket_bindings" {
  for_each = var.iam_bindings

  bucket = google_storage_bucket.data_bucket.name
  role   = each.value.role

  members = each.value.members

  dynamic "condition" {
    for_each = each.value.condition != null ? [each.value.condition] : []
    content {
      title       = condition.value.title
      description = condition.value.description
      expression  = condition.value.expression
    }
  }
}