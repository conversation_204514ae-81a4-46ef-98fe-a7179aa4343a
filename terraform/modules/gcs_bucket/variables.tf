variable "bucket_name" {
  description = "Name of the GCS bucket"
  type        = string
  validation {
    condition     = can(regex("^[a-z0-9][a-z0-9-_]{1,61}[a-z0-9]$", var.bucket_name))
    error_message = "Bucket name must be 3-63 characters, start and end with alphanumeric, contain only lowercase letters, numbers, hyphens, and underscores."
  }
}

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "location" {
  description = "The location of the bucket (region or multi-region)"
  type        = string
  default     = "asia-southeast1"
}

variable "storage_class" {
  description = "The storage class of the bucket"
  type        = string
  default     = "STANDARD"
  validation {
    condition = contains([
      "STANDARD",
      "NEARLINE", 
      "COLDLINE",
      "ARCHIVE"
    ], var.storage_class)
    error_message = "Storage class must be one of: STANDARD, NEARLINE, COLDLINE, ARCHIVE."
  }
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "enable_versioning" {
  description = "Enable versioning on the bucket"
  type        = bool
  default     = true
}

variable "prevent_destroy" {
  description = "Prevent accidental deletion of the bucket"
  type        = bool
  default     = true
}

variable "lifecycle_rules" {
  description = "List of lifecycle rules to configure"
  type = list(object({
    action = object({
      type          = string
      storage_class = optional(string)
    })
    condition = object({
      age                        = optional(number)
      created_before             = optional(string)
      with_state                 = optional(string)
      matches_storage_class      = optional(list(string))
      matches_prefix             = optional(list(string))
      matches_suffix             = optional(list(string))
      num_newer_versions         = optional(number)
      custom_time_before         = optional(string)
      days_since_custom_time     = optional(number)
      days_since_noncurrent_time = optional(number)
      noncurrent_time_before     = optional(string)
    })
  }))
  default = []
}

variable "cors_rules" {
  description = "CORS configuration for the bucket"
  type = list(object({
    origin          = list(string)
    method          = list(string)
    response_header = list(string)
    max_age_seconds = number
  }))
  default = []
}

variable "retention_policy" {
  description = "Retention policy for the bucket"
  type = object({
    retention_period = number
    is_locked        = bool
  })
  default = null
}

variable "kms_key_name" {
  description = "The Cloud KMS key name for customer-managed encryption"
  type        = string
  default     = null
}

variable "logging_config" {
  description = "Access logging configuration for the bucket"
  type = object({
    log_bucket        = string
    log_object_prefix = string
  })
  default = null
}

variable "website_config" {
  description = "Website configuration for the bucket"
  type = object({
    main_page_suffix = string
    not_found_page   = string
  })
  default = null
}

variable "labels" {
  description = "Labels to apply to the bucket"
  type        = map(string)
  default     = {}
}

# Notification configuration
variable "enable_notifications" {
  description = "Enable Cloud Storage notifications"
  type        = bool
  default     = false
}

variable "notification_topic" {
  description = "Pub/Sub topic for notifications"
  type        = string
  default     = null
}

variable "notification_events" {
  description = "List of event types to trigger notifications"
  type        = list(string)
  default = [
    "OBJECT_FINALIZE",
    "OBJECT_DELETE"
  ]
}

variable "notification_object_prefix" {
  description = "Object name prefix for notifications"
  type        = string
  default     = null
}

# IAM configuration
variable "iam_policy_data" {
  description = "IAM policy data in JSON format"
  type        = string
  default     = null
}

variable "iam_bindings" {
  description = "IAM bindings for the bucket"
  type = map(object({
    role    = string
    members = list(string)
    condition = optional(object({
      title       = string
      description = string
      expression  = string
    }))
  }))
  default = {}
}