output "bucket_name" {
  description = "Name of the created bucket"
  value       = google_storage_bucket.data_bucket.name
}

output "bucket_url" {
  description = "URL of the created bucket"
  value       = google_storage_bucket.data_bucket.url
}

output "bucket_self_link" {
  description = "Self-link of the created bucket"
  value       = google_storage_bucket.data_bucket.self_link
}

output "bucket_location" {
  description = "Location of the created bucket"
  value       = google_storage_bucket.data_bucket.location
}

output "bucket_storage_class" {
  description = "Storage class of the created bucket"
  value       = google_storage_bucket.data_bucket.storage_class
}

output "bucket_project" {
  description = "Project ID of the created bucket"
  value       = google_storage_bucket.data_bucket.project
}

output "bucket_versioning_enabled" {
  description = "Whether versioning is enabled on the bucket"
  value       = var.enable_versioning
}

output "bucket_uniform_bucket_level_access" {
  description = "Whether uniform bucket-level access is enabled"
  value       = google_storage_bucket.data_bucket.uniform_bucket_level_access
}

output "bucket_public_access_prevention" {
  description = "Public access prevention setting"
  value       = google_storage_bucket.data_bucket.public_access_prevention
}

output "notification_self_links" {
  description = "Self-links of the notification configurations"
  value       = google_storage_notification.function_notification[*].self_link
}