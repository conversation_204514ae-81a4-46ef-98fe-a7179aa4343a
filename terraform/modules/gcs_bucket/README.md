# GCS Bucket Module

This Terraform module creates and configures a Google Cloud Storage (GCS) bucket with security best practices, lifecycle management, and optional features.

## Features

- **Security**: Uniform bucket-level access, public access prevention enforced
- **Lifecycle Management**: Configurable lifecycle rules for cost optimization
- **Versioning**: Optional object versioning
- **Encryption**: Support for customer-managed encryption keys (CMEK)
- **Monitoring**: Integration with Cloud Storage notifications
- **IAM**: Fine-grained access control with IAM bindings
- **Compliance**: Retention policies and audit logging support

## Usage

### Basic Usage

```hcl
module "data_bucket" {
  source = "../../modules/gcs_bucket"
  
  bucket_name  = "my-data-bucket"
  project_id   = "my-project"
  location     = "asia-southeast1"
  environment  = "dev"
}
```

### Advanced Usage

```hcl
module "data_bucket" {
  source = "../../modules/gcs_bucket"
  
  bucket_name       = "tts-prod-data"
  project_id        = "my-tts-project"
  location          = "asia-southeast1"
  environment       = "prod"
  storage_class     = "STANDARD"
  enable_versioning = true
  prevent_destroy   = true
  
  # Lifecycle rules for cost optimization
  lifecycle_rules = [
    {
      action = {
        type = "Delete"
      }
      condition = {
        age            = 30
        matches_prefix = ["_tmp/"]
      }
    },
    {
      action = {
        type          = "SetStorageClass"
        storage_class = "NEARLINE"
      }
      condition = {
        age = 30
        matches_prefix = ["archive/"]
      }
    }
  ]
  
  # IAM bindings
  iam_bindings = {
    viewers = {
      role = "roles/storage.objectViewer"
      members = [
        "serviceAccount:<EMAIL>"
      ]
    }
    admins = {
      role = "roles/storage.admin"
      members = [
        "serviceAccount:<EMAIL>"
      ]
      condition = {
        title       = "Restrict to specific prefix"
        description = "Only allow access to books/ prefix"
        expression  = "resource.name.startsWith(\"projects/_/buckets/my-bucket/objects/books/\")"
      }
    }
  }
  
  # Custom labels
  labels = {
    team        = "platform"
    cost_center = "engineering"
    backup      = "daily"
  }
}
```

## Lifecycle Rules

The module supports comprehensive lifecycle management:

### Common Lifecycle Rules

```hcl
lifecycle_rules = [
  # Delete temporary files after 7 days
  {
    action = { type = "Delete" }
    condition = {
      age            = 7
      matches_prefix = ["_tmp/", "temp/"]
    }
  },
  
  # Move logs to Nearline after 30 days
  {
    action = {
      type          = "SetStorageClass"
      storage_class = "NEARLINE"
    }
    condition = {
      age            = 30
      matches_prefix = ["logs/"]
    }
  },
  
  # Archive old versions after 90 days
  {
    action = {
      type          = "SetStorageClass" 
      storage_class = "ARCHIVE"
    }
    condition = {
      age                = 90
      with_state         = "ARCHIVED"
      num_newer_versions = 3
    }
  },
  
  # Delete non-current versions after 365 days
  {
    action = { type = "Delete" }
    condition = {
      age        = 365
      with_state = "ARCHIVED"
    }
  }
]
```

## Security Features

### Default Security Settings

- **Uniform Bucket-Level Access**: Enabled by default
- **Public Access Prevention**: Enforced by default  
- **Versioning**: Enabled by default for data protection

### IAM Conditions

The module supports IAM conditions for fine-grained access control:

```hcl
iam_bindings = {
  restricted_access = {
    role = "roles/storage.objectViewer"
    members = ["serviceAccount:<EMAIL>"]
    condition = {
      title       = "Books folder only"
      description = "Restrict access to books/ prefix only"
      expression  = "resource.name.startsWith(\"projects/_/buckets/bucket-name/objects/books/\")"
    }
  }
}
```

### Customer-Managed Encryption

```hcl
module "encrypted_bucket" {
  source = "../../modules/gcs_bucket"
  
  bucket_name  = "secure-bucket"
  project_id   = "my-project"
  kms_key_name = "projects/my-project/locations/global/keyRings/my-ring/cryptoKeys/my-key"
}
```

## Monitoring Integration

### Cloud Storage Notifications

```hcl
module "monitored_bucket" {
  source = "../../modules/gcs_bucket"
  
  bucket_name           = "monitored-bucket"
  project_id            = "my-project"
  enable_notifications  = true
  notification_topic    = "projects/my-project/topics/storage-events"
  notification_events   = ["OBJECT_FINALIZE", "OBJECT_DELETE"]
  notification_object_prefix = "books/"
}
```

## Compliance Features

### Retention Policy

```hcl
module "compliant_bucket" {
  source = "../../modules/gcs_bucket"
  
  bucket_name = "compliance-bucket"
  project_id  = "my-project"
  
  retention_policy = {
    retention_period = 86400  # 1 day in seconds
    is_locked        = false  # Set to true for immutable policy
  }
}
```

### Audit Logging

```hcl
module "logged_bucket" {
  source = "../../modules/gcs_bucket"
  
  bucket_name = "logged-bucket"
  project_id  = "my-project"
  
  logging_config = {
    log_bucket        = "my-audit-logs-bucket"
    log_object_prefix = "access-logs/"
  }
}
```

## Input Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| bucket_name | Name of the GCS bucket | string | - | yes |
| project_id | The GCP project ID | string | - | yes |
| location | The location of the bucket | string | "asia-southeast1" | no |
| storage_class | The storage class of the bucket | string | "STANDARD" | no |
| environment | Environment name | string | - | yes |
| enable_versioning | Enable versioning on the bucket | bool | true | no |
| prevent_destroy | Prevent accidental deletion | bool | true | no |
| lifecycle_rules | List of lifecycle rules | list | [] | no |
| cors_rules | CORS configuration | list | [] | no |
| retention_policy | Retention policy configuration | object | null | no |
| kms_key_name | KMS key for encryption | string | null | no |
| labels | Labels to apply | map | {} | no |

## Outputs

| Name | Description |
|------|-------------|
| bucket_name | Name of the created bucket |
| bucket_url | URL of the created bucket |
| bucket_self_link | Self-link of the created bucket |
| bucket_location | Location of the created bucket |
| bucket_storage_class | Storage class of the created bucket |

## Examples

See the `examples/` directory for complete usage examples:

- `examples/basic/` - Simple bucket creation
- `examples/advanced/` - Full-featured configuration
- `examples/compliance/` - Compliance-focused setup
- `examples/multi-region/` - Multi-region deployment

## Best Practices

1. **Naming**: Use environment prefixes for bucket names
2. **Security**: Always enable uniform bucket-level access
3. **Cost**: Configure lifecycle rules for unused data
4. **Monitoring**: Enable notifications for critical buckets
5. **Backup**: Enable versioning for important data
6. **Compliance**: Use retention policies for regulated data

## Migration Notes

When migrating from existing buckets:

1. Import existing bucket: `terraform import module.bucket.google_storage_bucket.data_bucket bucket-name`
2. Plan changes carefully to avoid data loss
3. Test lifecycle rules in non-production first
4. Consider data transfer costs for location changes