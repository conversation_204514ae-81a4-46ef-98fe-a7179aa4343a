output "secret_names" {
  description = "Names of created secrets"
  value = {
    for key, secret in google_secret_manager_secret.secrets :
    key => secret.name
  }
}

output "secret_ids" {
  description = "IDs of created secrets"
  value = {
    for key, secret in google_secret_manager_secret.secrets :
    key => secret.id
  }
}

output "secret_short_names" {
  description = "Short names (secret IDs) of created secrets for use in code"
  value = {
    for key, secret in google_secret_manager_secret.secrets :
    key => secret.secret_id
  }
}

output "secret_self_links" {
  description = "Self-links of created secrets"
  value = {
    for key, secret in google_secret_manager_secret.secrets :
    key => "https://secretmanager.googleapis.com/v1/${secret.id}"
  }
}

output "secret_version_ids" {
  description = "IDs of created secret versions (if any)"
  value = {
    for key, version in google_secret_manager_secret_version.secret_versions :
    key => version.id
  }
}

output "secret_version_names" {
  description = "Names of created secret versions (if any)"
  value = {
    for key, version in google_secret_manager_secret_version.secret_versions :
    key => version.name
  }
}