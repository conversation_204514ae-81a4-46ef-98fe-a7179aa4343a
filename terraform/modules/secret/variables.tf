variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "secrets" {
  description = "Map of secrets to create and manage"
  type = map(object({
    secret_id                = string
    replica_locations        = optional(list(string))  # Empty list for automatic replication
    kms_key_name            = optional(string)         # For customer-managed encryption
    ttl                     = optional(number)         # TTL in seconds
    expire_time             = optional(string)         # RFC3339 format
    rotation_period         = optional(string)         # e.g., "86400s" for daily
    next_rotation_time      = optional(string)         # RFC3339 format
    notification_topics     = optional(list(string))   # Pub/Sub topics for notifications
    annotations             = optional(map(string))    # Metadata annotations
    labels                  = optional(map(string))    # Resource labels
    create_initial_version  = optional(bool, false)    # Create placeholder version
    initial_value          = optional(string)         # Initial secret value (use with caution)
    
    # IAM bindings for the secret
    iam_bindings = optional(map(object({
      role    = string
      members = list(string)
      condition = optional(object({
        title       = string
        description = string
        expression  = string
      }))
    })), {})
  }))
  default = {}

  validation {
    condition = alltrue([
      for secret_key, secret_config in var.secrets :
      can(regex("^[a-zA-Z][a-zA-Z0-9_-]*[a-zA-Z0-9]$", secret_config.secret_id))
    ])
    error_message = "Secret IDs must start with a letter, end with a letter or number, and contain only letters, numbers, underscores, and hyphens."
  }
}

variable "labels" {
  description = "Default labels to apply to all secrets"
  type        = map(string)
  default     = {}
}