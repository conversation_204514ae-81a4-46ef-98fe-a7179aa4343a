terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Enable Secret Manager API
resource "google_project_service" "secretmanager" {
  project = var.project_id
  service = "secretmanager.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy        = false
}

# Secret Manager Secrets
resource "google_secret_manager_secret" "secrets" {
  for_each = var.secrets

  project   = var.project_id
  secret_id = each.value.secret_id

  # Replication configuration
  replication {
    dynamic "user_managed" {
      for_each = length(coalesce(each.value.replica_locations, [])) > 0 ? [1] : []
      content {
        dynamic "replicas" {
          for_each = coalesce(each.value.replica_locations, [])
          content {
            location = replicas.value
            
            # Customer-managed encryption (optional)
            dynamic "customer_managed_encryption" {
              for_each = each.value.kms_key_name != null ? [1] : []
              content {
                kms_key_name = each.value.kms_key_name
              }
            }
          }
        }
      }
    }

    dynamic "auto" {
      for_each = length(coalesce(each.value.replica_locations, [])) == 0 ? [1] : []
      content {
        # Customer-managed encryption for automatic replication (optional)
        dynamic "customer_managed_encryption" {
          for_each = each.value.kms_key_name != null ? [1] : []
          content {
            kms_key_name = each.value.kms_key_name
          }
        }
      }
    }
  }

  # TTL for automatic secret deletion (optional)
  # Note: TTL block is not supported in current provider version

  # Expiration time (alternative to TTL)
  expire_time = each.value.expire_time

  # Rotation configuration
  dynamic "rotation" {
    for_each = each.value.rotation_period != null ? [1] : []
    content {
      rotation_period = each.value.rotation_period
      next_rotation_time = each.value.next_rotation_time
    }
  }

  # Topics for notifications on secret changes
  dynamic "topics" {
    for_each = coalesce(each.value.notification_topics, [])
    content {
      name = topics.value
    }
  }

  # Annotations (metadata)
  annotations = coalesce(each.value.annotations, {})

  # Labels
  labels = merge(
    {
      environment = var.environment
      managed_by  = "terraform"
    },
    var.labels,
    coalesce(each.value.labels, {})
  )

  depends_on = [google_project_service.secretmanager]
}

# Secret Manager Secret Versions (placeholder - actual values should be set via CI/CD)
# Note: We only create empty versions to establish the secret structure
resource "google_secret_manager_secret_version" "secret_versions" {
  for_each = {
    for k, v in var.secrets : k => v
    if lookup(v, "create_initial_version", false)
  }

  secret = google_secret_manager_secret.secrets[each.key].id
  
  # Use placeholder value - real value should be set via gcloud or CI/CD
  secret_data = lookup(each.value, "initial_value", "PLACEHOLDER_REPLACE_ME")

  # Disable this version if it's just a placeholder
  enabled = lookup(each.value, "initial_value", "PLACEHOLDER_REPLACE_ME") != "PLACEHOLDER_REPLACE_ME"

  lifecycle {
    ignore_changes = [secret_data, enabled]
  }
}

# IAM bindings for secrets
resource "google_secret_manager_secret_iam_binding" "secret_bindings" {
  for_each = {
    for binding_key, binding_value in local.secret_iam_bindings : binding_key => binding_value
  }

  project   = var.project_id
  secret_id = each.value.secret_id
  role      = each.value.role
  members   = each.value.members

  dynamic "condition" {
    for_each = each.value.condition != null ? [each.value.condition] : []
    content {
      title       = condition.value.title
      description = condition.value.description
      expression  = condition.value.expression
    }
  }

  depends_on = [google_secret_manager_secret.secrets]
}

# Local values for processing IAM bindings
locals {
  # Flatten IAM bindings from nested structure
  secret_iam_bindings = merge([
    for secret_key, secret_config in var.secrets : {
      for binding_key, binding_config in lookup(secret_config, "iam_bindings", {}) :
      "${secret_key}_${binding_key}" => merge(binding_config, {
        secret_id = secret_config.secret_id
      })
    }
  ]...)
}