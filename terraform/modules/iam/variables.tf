variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "data_bucket_name" {
  description = "Name of the data storage bucket"
  type        = string
}

variable "chapter_topic_name" {
  description = "Name of the chapter processing Pub/Sub topic"
  type        = string
}

variable "chapter_text_extraction_topic_name" {
  description = "Name of the chapter text extraction Pub/Sub topic"
  type        = string
}

variable "gemini_secret_name" {
  description = "Name of the Gemini API key secret in Secret Manager"
  type        = string
}

variable "region" {
  description = "The GCP region where resources are deployed"
  type        = string
}

variable "pdf_function_name" {
  description = "Name of the PDF processing function"
  type        = string
}

variable "tts_function_name" {
  description = "Name of the TTS processing function"
  type        = string
}

variable "chapter_extraction_function_name" {
  description = "Name of the chapter text extraction function"
  type        = string
}

variable "enable_iam_conditions" {
  description = "Enable IAM conditions for fine-grained access control"
  type        = bool
  default     = true
}

variable "create_sa_keys" {
  description = "Create service account keys for external integrations"
  type        = bool
  default     = false
}

variable "sa_key_rotation_time" {
  description = "Service account key rotation timestamp (forces key recreation)"
  type        = string
  default     = "2024-01-01"
}

variable "additional_project_iam_bindings" {
  description = "Additional project-level IAM bindings"
  type = map(object({
    role   = string
    member = string
    condition = optional(object({
      title       = string
      description = string
      expression  = string
    }))
  }))
  default = {}
}

# Service account naming (optional customization)
variable "pdf_sa_account_id" {
  description = "Account ID for PDF processing service account"
  type        = string
  default     = null  # Will use default naming: sa-tts-{environment}-pdf
  validation {
    condition = var.pdf_sa_account_id == null || can(regex("^[a-z][a-z0-9-]*[a-z0-9]$", var.pdf_sa_account_id))
    error_message = "Service account ID must start with a letter, end with alphanumeric, and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "tts_sa_account_id" {
  description = "Account ID for TTS processing service account"
  type        = string
  default     = null  # Will use default naming: sa-tts-{environment}-tts
  validation {
    condition = var.tts_sa_account_id == null || can(regex("^[a-z][a-z0-9-]*[a-z0-9]$", var.tts_sa_account_id))
    error_message = "Service account ID must start with a letter, end with alphanumeric, and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "chapter_extraction_sa_account_id" {
  description = "Account ID for chapter text extraction service account"
  type        = string
  default     = null  # Will use default naming: sa-tts-{environment}-chapter-extraction
  validation {
    condition = var.chapter_extraction_sa_account_id == null || can(regex("^[a-z][a-z0-9-]*[a-z0-9]$", var.chapter_extraction_sa_account_id))
    error_message = "Service account ID must start with a letter, end with alphanumeric, and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "admin_sa_account_id" {
  description = "Account ID for admin publisher service account"
  type        = string
  default     = null  # Will use default naming: sa-tts-{environment}-admin
  validation {
    condition = var.admin_sa_account_id == null || can(regex("^[a-z][a-z0-9-]*[a-z0-9]$", var.admin_sa_account_id))
    error_message = "Service account ID must start with a letter, end with alphanumeric, and contain only lowercase letters, numbers, and hyphens."
  }
}

# Cross-project Firestore configuration
variable "firestore_project_id" {
  description = "Project ID where Firestore database is located (for cross-project access)"
  type        = string
}

variable "create_chapter_subscription" {
  description = "Whether to create IAM bindings for the chapter subscription. Defaults to false since Cloud Functions Gen 2 with Pub/Sub triggers automatically create push subscriptions."
  type        = bool
  default     = false
}