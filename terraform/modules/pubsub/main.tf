terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Dead Letter Topic (must be created first)
resource "google_pubsub_topic" "dead_letter_topic" {
  name    = var.dead_letter_topic_name
  project = var.project_id

  # Message retention configuration
  message_retention_duration = var.dead_letter_message_retention_duration

  labels = merge(
    {
      environment = var.environment
      managed_by  = "terraform"
      purpose     = "dead-letter-queue"
    },
    var.labels
  )
}

# Dead Letter Subscription for monitoring/debugging
resource "google_pubsub_subscription" "dead_letter_subscription" {
  name    = "${var.dead_letter_topic_name}-sub"
  topic   = google_pubsub_topic.dead_letter_topic.name
  project = var.project_id

  # Message retention configuration
  message_retention_duration   = var.dead_letter_message_retention_duration
  retain_acked_messages        = true
  ack_deadline_seconds         = 600  # 10 minutes for manual inspection
  enable_message_ordering      = false

  # Expiration policy to auto-cleanup unused subscription
  expiration_policy {
    ttl = "2592000s"  # 30 days - must be >= message retention duration
  }

  labels = merge(
    {
      environment = var.environment
      managed_by  = "terraform"
      purpose     = "dead-letter-monitoring"
    },
    var.labels
  )
}

# PDF Processing Topic
resource "google_pubsub_topic" "pdf_processing_topic" {
  name    = var.pdf_processing_topic_name
  project = var.project_id

  # Message retention configuration
  message_retention_duration = var.message_retention_duration

  labels = merge(
    {
      environment = var.environment
      managed_by  = "terraform"
      purpose     = "pdf-processing"
    },
    var.labels
  )
}

# Chapter Text Extraction Topic  
resource "google_pubsub_topic" "chapter_text_extraction_topic" {
  name    = var.chapter_text_extraction_topic_name
  project = var.project_id

  # Message retention configuration
  message_retention_duration = var.message_retention_duration

  labels = merge(
    {
      environment = var.environment
      managed_by  = "terraform"
      purpose     = "chapter-text-extraction"
    },
    var.labels
  )
}

# Main Chapter Processing Topic
resource "google_pubsub_topic" "chapter_topic" {
  name    = var.chapter_topic_name
  project = var.project_id

  # Message retention configuration
  message_retention_duration = var.message_retention_duration

  # Schema configuration (optional)
  dynamic "schema_settings" {
    for_each = var.schema_name != null ? [var.schema_name] : []
    content {
      schema   = schema_settings.value
      encoding = var.schema_encoding
    }
  }

  labels = merge(
    {
      environment = var.environment
      managed_by  = "terraform"
      purpose     = "chapter-processing"
    },
    var.labels
  )
}

# Chapter Processing Subscription with Dead Letter Queue
resource "google_pubsub_subscription" "chapter_subscription" {
  count = var.create_chapter_subscription ? 1 : 0
  name    = "${var.chapter_topic_name}-sub"
  topic   = google_pubsub_topic.chapter_topic.name
  project = var.project_id

  # Message processing configuration
  ack_deadline_seconds         = var.ack_deadline_seconds
  message_retention_duration   = var.message_retention_duration
  retain_acked_messages        = var.retain_acked_messages
  enable_message_ordering      = var.enable_message_ordering

  # Dead letter policy configuration
  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.dead_letter_topic.id
    max_delivery_attempts = var.max_delivery_attempts
  }

  # Retry policy configuration
  retry_policy {
    minimum_backoff = var.retry_minimum_backoff
    maximum_backoff = var.retry_maximum_backoff
  }

  # Push configuration (if specified)
  dynamic "push_config" {
    for_each = var.push_endpoint != null ? [var.push_endpoint] : []
    content {
      push_endpoint = push_config.value

      # OIDC token configuration for secure push
      dynamic "oidc_token" {
        for_each = var.push_oidc_service_account != null ? [var.push_oidc_service_account] : []
        content {
          service_account_email = oidc_token.value
          audience              = var.push_oidc_audience
        }
      }

      # Attributes for push requests
      attributes = var.push_attributes
    }
  }

  # Expiration policy (optional)
  dynamic "expiration_policy" {
    for_each = var.subscription_expiration_ttl != null ? [var.subscription_expiration_ttl] : []
    content {
      ttl = expiration_policy.value
    }
  }

  # BigQuery configuration (optional)
  dynamic "bigquery_config" {
    for_each = var.bigquery_table != null ? [var.bigquery_table] : []
    content {
      table                = bigquery_config.value
      use_topic_schema     = var.bigquery_use_topic_schema
      write_metadata       = var.bigquery_write_metadata
      drop_unknown_fields  = var.bigquery_drop_unknown_fields
    }
  }

  # Cloud Storage configuration (optional)
  dynamic "cloud_storage_config" {
    for_each = var.storage_bucket != null ? [var.storage_bucket] : []
    content {
      bucket          = cloud_storage_config.value
      filename_prefix = var.storage_filename_prefix
      filename_suffix = var.storage_filename_suffix
      max_duration    = var.storage_max_duration
      max_bytes       = var.storage_max_bytes
    }
  }

  labels = merge(
    {
      environment = var.environment
      managed_by  = "terraform"
      purpose     = "chapter-processing"
    },
    var.labels
  )

  depends_on = [google_pubsub_topic.dead_letter_topic]
}

# Schema definition (if specified)
resource "google_pubsub_schema" "message_schema" {
  count = var.schema_definition != null ? 1 : 0

  name       = var.schema_name
  type       = var.schema_type
  definition = var.schema_definition
  project    = var.project_id
}

# Topic IAM bindings
resource "google_pubsub_topic_iam_binding" "chapter_topic_bindings" {
  for_each = var.topic_iam_bindings

  project = var.project_id
  topic   = google_pubsub_topic.chapter_topic.name
  role    = each.value.role
  members = each.value.members

  dynamic "condition" {
    for_each = each.value.condition != null ? [each.value.condition] : []
    content {
      title       = condition.value.title
      description = condition.value.description
      expression  = condition.value.expression
    }
  }
}

# Subscription IAM bindings  
resource "google_pubsub_subscription_iam_binding" "chapter_subscription_bindings" {
  for_each = var.create_chapter_subscription ? var.subscription_iam_bindings : {}

  project      = var.project_id
  subscription = google_pubsub_subscription.chapter_subscription[0].name
  role         = each.value.role
  members      = each.value.members

  dynamic "condition" {
    for_each = each.value.condition != null ? [each.value.condition] : []
    content {
      title       = condition.value.title
      description = condition.value.description
      expression  = condition.value.expression
    }
  }
}