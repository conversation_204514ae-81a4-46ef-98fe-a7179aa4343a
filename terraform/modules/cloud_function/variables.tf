variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The GCP region for the function"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

# Function basic configuration
variable "function_name" {
  description = "Name of the Cloud Function"
  type        = string
  validation {
    condition     = can(regex("^[a-z][a-z0-9-]*[a-z0-9]$", var.function_name))
    error_message = "Function name must start with a letter, end with alphanumeric, and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "description" {
  description = "Description of the function"
  type        = string
  default     = ""
}

variable "runtime" {
  description = "Runtime for the function"
  type        = string
  default     = "python312"
  validation {
    condition = contains([
      "python38", "python39", "python310", "python311", "python312",
      "nodejs16", "nodejs18", "nodejs20",
      "go116", "go118", "go119", "go120", "go121",
      "java11", "java17",
      "dotnet3", "dotnet6"
    ], var.runtime)
    error_message = "Runtime must be a supported Cloud Functions runtime."
  }
}

variable "entry_point" {
  description = "Entry point function name"
  type        = string
}

# Resource configuration
variable "memory" {
  description = "Memory allocation for the function (e.g., '512Mi', '1Gi')"
  type        = string
  default     = "512Mi"
  validation {
    condition     = can(regex("^[0-9]+[MG]i$", var.memory))
    error_message = "Memory must be specified with Mi or Gi units (e.g., '512Mi', '2Gi')."
  }
}

variable "cpu" {
  description = "CPU allocation for the function (e.g., '1', '2', '0.5')"
  type        = string
  default     = "1"
}

variable "timeout" {
  description = "Function timeout (e.g., '60s', '540s')"
  type        = string
  default     = "60s"
  validation {
    condition     = can(regex("^[0-9]+s$", var.timeout))
    error_message = "Timeout must be specified in seconds (e.g., '60s', '540s')."
  }
}

variable "max_instances" {
  description = "Maximum number of instances"
  type        = number
  default     = 100
  validation {
    condition     = var.max_instances >= 1 && var.max_instances <= 3000
    error_message = "Max instances must be between 1 and 3000."
  }
}

variable "min_instances" {
  description = "Minimum number of instances"
  type        = number
  default     = 0
  validation {
    condition     = var.min_instances >= 0 && var.min_instances <= 1000
    error_message = "Min instances must be between 0 and 1000."
  }
}

variable "concurrency" {
  description = "Maximum concurrent requests per instance"
  type        = number
  default     = 1
  validation {
    condition     = var.concurrency >= 1 && var.concurrency <= 1000
    error_message = "Concurrency must be between 1 and 1000."
  }
}

# Source configuration
variable "source_dir" {
  description = "Directory containing the function source code"
  type        = string
  default     = null
}

variable "source_excludes" {
  description = "List of files/patterns to exclude from source archive"
  type        = list(string)
  default = [
    "**/__pycache__/**",
    "**/.git/**",
    "**/.gitignore",
    "**/README.md",
    "**/.DS_Store",
    "**/node_modules/**",
    "**/.pytest_cache/**",
    "**/.mypy_cache/**",
    "**/venv/**",
    "**/.venv/**"
  ]
}

variable "source_repository_url" {
  description = "URL of the source repository (alternative to source_dir)"
  type        = string
  default     = null
}

variable "source_branch" {
  description = "Branch name for repository source"
  type        = string
  default     = "main"
}

variable "source_directory" {
  description = "Directory within the repository"
  type        = string
  default     = "/"
}

variable "create_source_bucket" {
  description = "Create a bucket for storing function source code"
  type        = bool
  default     = true
}

# Build configuration
variable "build_environment_variables" {
  description = "Environment variables for the build process"
  type        = map(string)
  default     = {}
}

variable "use_docker" {
  description = "Use Docker container for function deployment"
  type        = bool
  default     = false
}

variable "docker_repository" {
  description = "Docker repository for the function image"
  type        = string
  default     = null
}

variable "worker_pool" {
  description = "Cloud Build worker pool for private builds"
  type        = string
  default     = null
}

# Runtime configuration
variable "environment_variables" {
  description = "Environment variables for the function"
  type        = map(string)
  default     = {}
}

variable "secret_environment_variables" {
  description = "Secret environment variables from Secret Manager"
  type = list(object({
    key        = string
    project_id = string
    secret     = string
    version    = string
  }))
  default = []
}

variable "secret_volumes" {
  description = "Secret volumes to mount in the function"
  type = list(object({
    mount_path = string
    project_id = string
    secret     = string
    versions = list(object({
      version = string
      path    = string
    }))
  }))
  default = []
}

# Networking configuration
variable "service_account_email" {
  description = "Service account email for the function"
  type        = string
}

variable "ingress_settings" {
  description = "Ingress settings for the function"
  type        = string
  default     = "ALLOW_INTERNAL_ONLY"
  validation {
    condition = contains([
      "ALLOW_ALL",
      "ALLOW_INTERNAL_ONLY",
      "ALLOW_INTERNAL_AND_GCLB"
    ], var.ingress_settings)
    error_message = "Ingress settings must be ALLOW_ALL, ALLOW_INTERNAL_ONLY, or ALLOW_INTERNAL_AND_GCLB."
  }
}

variable "vpc_connector" {
  description = "VPC connector for the function"
  type        = string
  default     = null
}

variable "vpc_connector_egress_settings" {
  description = "VPC connector egress settings"
  type        = string
  default     = "PRIVATE_RANGES_ONLY"
  validation {
    condition = contains([
      "PRIVATE_RANGES_ONLY",
      "ALL_TRAFFIC"
    ], var.vpc_connector_egress_settings)
    error_message = "VPC connector egress settings must be PRIVATE_RANGES_ONLY or ALL_TRAFFIC."
  }
}

# Trigger configuration
variable "trigger_config" {
  description = "Event trigger configuration"
  type = object({
    event_type     = string
    resource       = optional(string)  # For Pub/Sub topic
    retry_policy   = optional(string)  # RETRY_POLICY_RETRY or RETRY_POLICY_DO_NOT_RETRY
    event_filters = optional(list(object({
      attribute = string
      value     = string
      operator  = optional(string)
    })))
  })
  default = null
}

# Access control
variable "allow_unauthenticated_invocations" {
  description = "Allow unauthenticated invocations (for HTTP functions)"
  type        = bool
  default     = false
}

variable "function_iam_bindings" {
  description = "IAM bindings for the function"
  type = map(object({
    role    = string
    members = list(string)
    condition = optional(object({
      title       = string
      description = string
      expression  = string
    }))
  }))
  default = {}
}

# Labels
variable "labels" {
  description = "Labels to apply to the function"
  type        = map(string)
  default     = {}
}