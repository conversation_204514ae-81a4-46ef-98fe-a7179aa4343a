output "function_name" {
  description = "Name of the Cloud Function"
  value       = google_cloudfunctions2_function.function.name
}

output "function_id" {
  description = "ID of the Cloud Function"
  value       = google_cloudfunctions2_function.function.id
}

output "function_uri" {
  description = "URI of the Cloud Function"
  value       = google_cloudfunctions2_function.function.service_config[0].uri
}

output "function_url" {
  description = "HTTPS URL of the Cloud Function (for HTTP triggers)"
  value       = google_cloudfunctions2_function.function.url
}

output "function_state" {
  description = "State of the Cloud Function"
  value       = google_cloudfunctions2_function.function.state
}

output "function_update_time" {
  description = "Last update time of the Cloud Function"
  value       = google_cloudfunctions2_function.function.update_time
}

output "function_service_account" {
  description = "Service account email used by the function"
  value       = google_cloudfunctions2_function.function.service_config[0].service_account_email
}

output "source_bucket_name" {
  description = "Name of the source code bucket (if created)"
  value       = var.create_source_bucket ? google_storage_bucket.source_bucket[0].name : null
}

output "source_object_name" {
  description = "Name of the source code object (if created)"
  value       = var.source_dir != null && var.create_source_bucket ? google_storage_bucket_object.source_object[0].name : null
}

output "source_archive_hash" {
  description = "MD5 hash of the source archive"
  value       = var.source_dir != null ? data.archive_file.source_archive[0].output_md5 : null
}

output "build_config" {
  description = "Build configuration of the function"
  value = {
    runtime     = google_cloudfunctions2_function.function.build_config[0].runtime
    entry_point = google_cloudfunctions2_function.function.build_config[0].entry_point
  }
}

output "service_config" {
  description = "Service configuration of the function"
  value = {
    max_instance_count               = google_cloudfunctions2_function.function.service_config[0].max_instance_count
    min_instance_count               = google_cloudfunctions2_function.function.service_config[0].min_instance_count
    available_memory                 = google_cloudfunctions2_function.function.service_config[0].available_memory
    timeout_seconds                  = google_cloudfunctions2_function.function.service_config[0].timeout_seconds
    max_instance_request_concurrency = google_cloudfunctions2_function.function.service_config[0].max_instance_request_concurrency
    available_cpu                    = google_cloudfunctions2_function.function.service_config[0].available_cpu
  }
}