terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
  }
}

# Enable required APIs
resource "google_project_service" "cloudfunctions" {
  project = var.project_id
  service = "cloudfunctions.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy        = false
}

resource "google_project_service" "cloudrun" {
  project = var.project_id
  service = "run.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy        = false
}

resource "google_project_service" "eventarc" {
  project = var.project_id
  service = "eventarc.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy        = false
}

resource "google_project_service" "cloudbuild" {
  project = var.project_id
  service = "cloudbuild.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy        = false
}

# Storage bucket for function source code
resource "google_storage_bucket" "source_bucket" {
  count = var.create_source_bucket ? 1 : 0

  name                        = "${var.project_id}-functions-source"
  location                    = var.region
  uniform_bucket_level_access = true
  public_access_prevention    = "enforced"

  lifecycle {
    prevent_destroy = false
  }

  depends_on = [google_project_service.cloudfunctions]
}

# Create source code archive
data "archive_file" "source_archive" {
  count = var.source_dir != null ? 1 : 0

  type        = "zip"
  source_dir  = var.source_dir
  output_path = "${path.module}/tmp/${var.function_name}-source.zip"
  excludes    = var.source_excludes

  depends_on = [google_project_service.cloudfunctions]
}

# Upload source code to bucket
resource "google_storage_bucket_object" "source_object" {
  count = var.source_dir != null && var.create_source_bucket ? 1 : 0

  name   = "${var.function_name}-${data.archive_file.source_archive[0].output_md5}.zip"
  bucket = google_storage_bucket.source_bucket[0].name
  source = data.archive_file.source_archive[0].output_path

  depends_on = [data.archive_file.source_archive]
}

# Cloud Function Gen 2
resource "google_cloudfunctions2_function" "function" {
  name        = var.function_name
  location    = var.region
  project     = var.project_id
  description = var.description

  build_config {
    # For Docker builds, still specify runtime but Cloud Build will use Dockerfile
    runtime     = var.runtime
    entry_point = var.entry_point

    # Source configuration
    dynamic "source" {
      for_each = var.source_dir != null && var.create_source_bucket ? [1] : []
      content {
        storage_source {
          bucket = google_storage_bucket.source_bucket[0].name
          object = google_storage_bucket_object.source_object[0].name
        }
      }
    }

    dynamic "source" {
      for_each = var.source_repository_url != null ? [1] : []
      content {
        repo_source {
          repo_name   = var.source_repository_url
          branch_name = var.source_branch
          dir         = var.source_directory
        }
      }
    }

    # Environment variables for build
    environment_variables = var.build_environment_variables

    # Docker repository configuration (required for Docker builds)
    docker_repository = var.use_docker ? (var.docker_repository != null ? var.docker_repository : "projects/${var.project_id}/locations/${var.region}/repositories/gcf-artifacts") : var.docker_repository
    
    # Worker pool for private builds
    worker_pool = var.worker_pool
  }

  service_config {
    # Compute resources
    max_instance_count               = var.max_instances
    min_instance_count               = var.min_instances
    available_memory                 = var.memory
    timeout_seconds                  = parseint(replace(var.timeout, "s", ""), 10)
    max_instance_request_concurrency = var.concurrency
    available_cpu                    = var.cpu

    # Environment variables
    environment_variables = var.environment_variables

    # Secret environment variables
    dynamic "secret_environment_variables" {
      for_each = var.secret_environment_variables
      content {
        key        = secret_environment_variables.value.key
        project_id = secret_environment_variables.value.project_id
        secret     = secret_environment_variables.value.secret
        version    = secret_environment_variables.value.version
      }
    }

    # Secret volumes
    dynamic "secret_volumes" {
      for_each = var.secret_volumes
      content {
        mount_path = secret_volumes.value.mount_path
        project_id = secret_volumes.value.project_id
        secret     = secret_volumes.value.secret

        dynamic "versions" {
          for_each = secret_volumes.value.versions
          content {
            version = versions.value.version
            path    = versions.value.path
          }
        }
      }
    }

    # Service account
    service_account_email = var.service_account_email

    # Ingress settings
    ingress_settings = var.ingress_settings

    # VPC connector - only set egress settings if connector is specified
    vpc_connector                 = var.vpc_connector
    vpc_connector_egress_settings = var.vpc_connector != null ? var.vpc_connector_egress_settings : null

    # All traffic on latest revision
    all_traffic_on_latest_revision = true
  }


  # Event trigger configuration
  dynamic "event_trigger" {
    for_each = var.trigger_config != null ? [var.trigger_config] : []
    content {
      trigger_region        = var.region
      event_type           = event_trigger.value.event_type
      retry_policy         = event_trigger.value.retry_policy
      service_account_email = var.service_account_email
      
      # For Pub/Sub triggers, specify the topic resource
      pubsub_topic = contains(["google.cloud.pubsub.topic.v1.messagePublished"], event_trigger.value.event_type) ? event_trigger.value.resource : null

      # Event filters
      dynamic "event_filters" {
        for_each = event_trigger.value.event_filters != null ? event_trigger.value.event_filters : []
        content {
          attribute = event_filters.value.attribute
          value     = event_filters.value.value
          operator  = lookup(event_filters.value, "operator", "=")
        }
      }
    }
  }

  # Labels
  labels = merge(
    {
      environment = var.environment
      managed_by  = "terraform"
      function    = var.function_name
    },
    var.labels
  )

  depends_on = [
    google_project_service.cloudfunctions,
    google_project_service.cloudrun,
    google_project_service.eventarc,
    google_project_service.cloudbuild
  ]
}

# Cloud Run service IAM (for HTTP functions)
resource "google_cloud_run_service_iam_member" "invoker" {
  count = var.allow_unauthenticated_invocations ? 1 : 0

  project  = var.project_id
  location = var.region
  service  = google_cloudfunctions2_function.function.name
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Function-level IAM bindings
resource "google_cloudfunctions2_function_iam_binding" "function_iam" {
  for_each = var.function_iam_bindings

  project        = var.project_id
  location       = var.region
  cloud_function = google_cloudfunctions2_function.function.name
  role           = each.value.role
  members        = each.value.members

  dynamic "condition" {
    for_each = each.value.condition != null ? [each.value.condition] : []
    content {
      title       = condition.value.title
      description = condition.value.description
      expression  = condition.value.expression
    }
  }
}

# Clean up temporary files
resource "null_resource" "cleanup" {
  count = var.source_dir != null ? 1 : 0

  triggers = {
    source_hash = data.archive_file.source_archive[0].output_md5
    archive_path = data.archive_file.source_archive[0].output_path
  }

  provisioner "local-exec" {
    command = "rm -f ${self.triggers.archive_path}"
    when    = destroy
  }

  depends_on = [google_cloudfunctions2_function.function]
}