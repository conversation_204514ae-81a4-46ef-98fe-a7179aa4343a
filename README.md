# Book Text-to-Speech System

Terraform-managed Cloud Functions project that automatically processes PDF books into individual chapter JSON files and generates audio files via text-to-speech processing. The system uses event-driven architecture with two specialized functions connected via Pub/Sub messaging.

## Quick Start

1. **Prerequisites**
   ```bash
   # Install required tools
   gcloud auth login
   export TF_VAR_project_id=fonos-audio
   ```

2. **Deploy Infrastructure**
   ```bash
   cd terraform
   ./deploy-dev.sh                      # Deploys complete system
   ```

3. **Test Processing**
   ```bash
   # Upload PDF (no automatic trigger)
   gsutil cp book.pdf gs://fonos-dev/books/test-book-001/book.pdf

   # Trigger processing manually
   cd scripts
   python trigger_pdf_processing.py \
     --book-id "test-book-001" \
     --title "Test Book" \
     --author "Test Author"

   # Monitor logs
   gcloud functions logs read --region=asia-southeast1
   ```

## Architecture

### Two-Function Design

**📄 PDF Processing Function** (`functions/pdf-to-text/`)
- **Trigger**: Pub/Sub messages from manual trigger script
- **Purpose**: Extract chapters using Google Generative AI
- **Output**: Chapter JSON files + Pub/Sub messages
- **Technology**: Standard Python deployment

**🔊 TTS Processing Function** (`functions/chapter-tts/`)  
- **Trigger**: Pub/Sub messages from PDF function
- **Purpose**: Convert chapter text to audio files
- **Output**: MP3 audio files 
- **Technology**: Docker container with ffmpeg + TTS processor

### Processing Flow (Manual Trigger Approach)

```mermaid
graph TD
    A[PDF Upload to GCS] --> B[Manual Trigger Script]
    B --> C[Firestore Record]
    B --> D[Pub/Sub Message]
    D --> E[PDF Function]
    E --> F[Chapter Extraction]
    F --> G[Chapter Pub/Sub Messages]
    G --> H[TTS Function]
    H --> I[Audio Generation]
    I --> J[MP3 Files]

    E --> K[Firestore Status Updates]
    H --> K
```

**New Workflow (Avoids GCS Job Stealing):**
1. **PDF Upload** → `gs://bucket/books/[book_id]/book.pdf` (no automatic trigger)
2. **Manual Trigger** → Python script creates Firestore record and publishes Pub/Sub message
3. **PDF Function** → Triggered by Pub/Sub, extracts chapters, publishes chapter messages
4. **TTS Function** → Processes each chapter message, generates audio
5. **Progress Tracking** → Firestore documents track processing status

**Benefits:**
- No conflicts with other GCS-triggered functions
- Better control over when processing starts
- Easier integration with admin systems
- Explicit book metadata handling

### Output Structure
```
books/[book_id]/
├── book.pdf                     # Original PDF
├── book_manifest.json           # Processing status & outline  
├── text-extracted-chapters/     # Chapter JSON files
│   ├── chapter_01.json
│   ├── chapter_02.json
│   └── ...
└── audio/                       # Generated audio files
    ├── chapter_01.mp3
    ├── chapter_02.mp3
    └── ...
```

## Manual Trigger Script

The `scripts/trigger_pdf_processing.py` script provides manual control over PDF processing:

### Setup
```bash
cd scripts
pip install -r requirements.txt
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"
export GCP_PROJECT="your-project-id"
export PDF_PROCESSING_TOPIC="tts-dev-pdf-process"
```

### Usage
```bash
# Basic usage
python trigger_pdf_processing.py \
  --book-id "book-123" \
  --title "My Book" \
  --author "Author Name"

# With custom language
python trigger_pdf_processing.py \
  --book-id "book-456" \
  --title "Sách tiếng Việt" \
  --author "Tác giả" \
  --language "vi-VN"
```

### What it does
1. Creates a Firestore record with book metadata
2. Publishes a Pub/Sub message to trigger PDF processing
3. Returns operation status and message ID

See `scripts/README.md` for detailed documentation.

## Function Structure

### Organized Architecture
```
functions/
├── pdf-to-text/                 # Function 1: PDF Processing
│   ├── main.py                  # Entry point (process_pdf_request)
│   ├── pdf_to_chapter.py        # PDF processing logic
│   ├── requirements.txt         # Python dependencies
│   └── Dockerfile               # Container configuration
├── chapter-tts/                 # Function 2: TTS Processing
│   ├── main.py                  # Entry point (process_chapter_tts)
│   ├── requirements.txt         # Dependencies
│   ├── Dockerfile               # Container with ffmpeg
│   ├── build.sh                 # Syncs git submodule
│   └── google-voice/            # TTS processor (from git submodule)
├── shared/                      # Common utilities
│   ├── firestore_schema.py      # Data models
│   └── __init__.py
└── scripts/                     # Manual trigger scripts
    ├── trigger_pdf_processing.py # Main trigger script
    ├── example_usage.py         # Usage examples
    ├── requirements.txt         # Script dependencies
    └── README.md               # Script documentation
```

## Data Schemas

### Chapter JSON Schema
Each chapter is saved as a JSON file:

```json
{
  "book_id": "book_001",
  "chapter_id": "chapter_01",
  "chapter_number": 1,
  "chapter_title": "Introduction",
  "content": "extracted chapter text...",
  "word_count": 1250,
  "status": "completed",
  "created_at": "2024-01-15T10:30:00Z",
  "gcs_path": "books/book_001/text-extracted-chapters/chapter_01.json"
}
```

### Book Manifest Schema
Processing progress tracking:

```json
{
  "book_id": "book_001", 
  "total_chapters": 15,
  "pdf_processing_status": "completed",
  "tts_processing_status": "processing",
  "chapters_completed": 12,
  "chapters_failed": [3, 7],
  "created_at": "2024-01-15T10:30:00Z",
  "outline": [...]
}
```

## Deployment

### Infrastructure as Code
All infrastructure managed via Terraform:

```bash
cd terraform

# Deploy complete system (first time)
./deploy-dev.sh

# Update functions only  
./update-functions.sh dev

# Deploy to other environments
./deploy-dev.sh      # Development
# (staging/prod configs available)
```

### Manual Function Updates
```bash  
# Sync google-voice processor submodule
cd functions/chapter-tts
./build.sh

# Deploy via gcloud (alternative to Terraform)
gcloud functions deploy pdf-to-text --source=functions/pdf-to-text
gcloud functions deploy chapter-tts --source=functions/chapter-tts
```

## Development

### PDF Processing Function
```bash
cd functions/pdf-to-text

# Install dependencies
pip install -r requirements.txt

# Test locally (requires GEMINI_API_KEY)
python main.py
```

### TTS Processing Function
```bash  
cd functions/chapter-tts

# Sync google-voice processor from git submodule
./build.sh

# Install dependencies  
pip install -r requirements.txt

# Test Docker build
docker build -t chapter-tts .
```

### Code Quality
```bash
# Format code (in any function directory)
black .

# Type checking
mypy main.py

# Run tests
pytest
```

## Key Features

### ✅ **Event-Driven Processing**
- GCS triggers for automatic PDF processing
- Pub/Sub messaging for scalable chapter processing  
- Firestore state management and progress tracking

### ✅ **Organized Function Structure**
- Clean separation between PDF and TTS processing
- Docker containers for complex dependencies (ffmpeg)
- Git submodule integration for external components

### ✅ **Terraform Infrastructure**  
- Infrastructure as Code approach
- Multi-environment support (dev/staging/prod)
- Automatic API enablement and IAM configuration

### ✅ **Scalable Architecture**
- Parallel chapter processing  
- Dead letter queues for error handling
- Configurable concurrency and resource limits

### ✅ **Audio Processing**
- System-level ffmpeg via Docker containers
- google-voice TTS processor from git submodule
- Multiple audio format support

## Configuration

### Environment Variables (Managed by Terraform)
- `GEMINI_API_KEY` - Google Generative AI API key (Secret Manager)
- `ENVIRONMENT` - Environment name (dev/staging/prod)
- `DATA_BUCKET_NAME` - GCS bucket for file storage
- `CHAPTER_TOPIC_NAME` - Pub/Sub topic for chapter messages

### Project Setup
1. **Authentication**: `gcloud auth login`
2. **Set Project**: `export TF_VAR_project_id=fonos-audio`
3. **Deploy**: `cd terraform && ./deploy-dev.sh`

## Monitoring & Troubleshooting

### Check Processing Status
```bash
# Function logs
gcloud functions logs read --region=asia-southeast1

# GCS files
gsutil ls gs://fonos-dev/books/your-book-id/

# Pub/Sub metrics  
gcloud pubsub topics list
gcloud pubsub subscriptions list
```

### Common Issues
- **Missing API Key**: Verify Secret Manager has `gemini-api-key`
- **Docker Build**: Ensure `build.sh` synced google-voice processor
- **Memory Limits**: Large PDFs may need increased memory allocation
- **Timeout**: Very long books may exceed function timeout

## Technology Stack

- **☁️ Google Cloud Functions Gen 2** - Serverless function execution
- **🏗️ Terraform** - Infrastructure as Code management  
- **🤖 Google Generative AI (Gemini)** - PDF chapter extraction
- **🔊 Google Voice TTS Processor** - Audio synthesis via git submodule
- **📦 Docker** - Containerization for ffmpeg dependencies
- **📨 Pub/Sub** - Event-driven messaging between functions
- **🔥 Firestore** - Document database for state management  
- **☁️ Cloud Storage** - File storage for PDFs, JSONs, and audio

## Next Steps

The system is production-ready for:
- ✅ Automatic PDF processing  
- ✅ Parallel chapter extraction
- ✅ Audio file generation
- ✅ Progress tracking
- ✅ Multi-environment deployment

Consider adding:
- 📊 Monitoring dashboards  
- 🔔 Processing notifications
- 📱 Web interface for uploads
- 🎛️ Audio quality controls